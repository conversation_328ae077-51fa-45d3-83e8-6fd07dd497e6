#!/usr/bin/env python3
"""
Test script for ZIP file functionality in AIMetaHarvest.
This script tests the new ZIP file upload and processing features.
"""

import os
import sys
import tempfile
import zipfile
import csv
import json
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def create_test_csv(filename, rows=100):
    """Create a test CSV file"""
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['id', 'name', 'age', 'city', 'salary'])
        
        for i in range(rows):
            writer.writerow([
                i + 1,
                f'Person {i + 1}',
                20 + (i % 50),
                f'City {(i % 10) + 1}',
                30000 + (i * 1000)
            ])

def create_test_json(filename, records=50):
    """Create a test JSON file"""
    data = []
    for i in range(records):
        data.append({
            'id': i + 1,
            'product': f'Product {i + 1}',
            'price': 10.99 + (i * 0.5),
            'category': f'Category {(i % 5) + 1}',
            'in_stock': i % 2 == 0
        })
    
    with open(filename, 'w', encoding='utf-8') as jsonfile:
        json.dump(data, jsonfile, indent=2)

def create_test_zip():
    """Create a test ZIP file with multiple datasets"""
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    zip_path = os.path.join(temp_dir, 'test_datasets.zip')
    
    # Create test files
    csv_file1 = os.path.join(temp_dir, 'employees.csv')
    csv_file2 = os.path.join(temp_dir, 'customers.csv')
    json_file = os.path.join(temp_dir, 'products.json')
    
    create_test_csv(csv_file1, 150)
    create_test_csv(csv_file2, 200)
    create_test_json(json_file, 75)
    
    # Create ZIP file
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        zipf.write(csv_file1, 'employees.csv')
        zipf.write(csv_file2, 'customers.csv')
        zipf.write(json_file, 'products.json')
        
        # Add a README file (should be ignored)
        readme_path = os.path.join(temp_dir, 'README.txt')
        with open(readme_path, 'w') as f:
            f.write('This is a test dataset collection.')
        zipf.write(readme_path, 'README.txt')
    
    return zip_path, temp_dir

def test_zip_validation():
    """Test ZIP file validation"""
    print("Testing ZIP file validation...")
    
    try:
        from app.utils.file_utils import validate_zip_contents, generate_collection_title
        
        # Create test ZIP
        zip_path, temp_dir = create_test_zip()
        
        # Test validation
        is_valid, error_msg, zip_info = validate_zip_contents(zip_path)
        
        print(f"ZIP validation result: {is_valid}")
        if is_valid:
            print(f"Total files: {zip_info['total_files']}")
            print(f"Dataset files: {zip_info['dataset_files']}")
            print(f"Total size: {zip_info['total_size']} bytes")
            print(f"File types: {zip_info['file_types']}")
            
            # Test title generation
            file_names = [f['name'] for f in zip_info['dataset_files_list']]
            title = generate_collection_title(file_names)
            print(f"Generated title: {title}")
            
        else:
            print(f"Validation error: {error_msg}")
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        
        return is_valid
        
    except Exception as e:
        print(f"Error testing ZIP validation: {e}")
        return False

def test_zip_extraction():
    """Test ZIP file extraction"""
    print("\nTesting ZIP file extraction...")
    
    try:
        from app.utils.file_utils import extract_zip_file
        
        # Create test ZIP
        zip_path, temp_dir = create_test_zip()
        
        # Test extraction
        success, extract_dir, extracted_files = extract_zip_file(zip_path)
        
        print(f"Extraction result: {success}")
        if success:
            print(f"Extracted to: {extract_dir}")
            print(f"Extracted files: {len(extracted_files)}")
            
            for file_info in extracted_files:
                print(f"  - {file_info['name']} ({file_info['extension']}, {file_info['size']} bytes)")
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        if success and extract_dir:
            shutil.rmtree(extract_dir)
        
        return success
        
    except Exception as e:
        print(f"Error testing ZIP extraction: {e}")
        return False

def test_file_size_limits():
    """Test file size limit updates"""
    print("\nTesting file size limits...")
    
    try:
        from app.utils.file_utils import MAX_FILE_SIZE, format_file_size
        from app.config import Config
        
        print(f"File utils MAX_FILE_SIZE: {format_file_size(MAX_FILE_SIZE)}")
        print(f"Config MAX_CONTENT_LENGTH: {format_file_size(Config.MAX_CONTENT_LENGTH)}")
        
        # Check if they match (5GB)
        expected_size = 5 * 1024 * 1024 * 1024
        
        if MAX_FILE_SIZE == expected_size and Config.MAX_CONTENT_LENGTH == expected_size:
            print("✓ File size limits correctly set to 5GB")
            return True
        else:
            print("✗ File size limits not correctly set")
            return False
            
    except Exception as e:
        print(f"Error testing file size limits: {e}")
        return False

def main():
    """Run all tests"""
    print("AIMetaHarvest ZIP Functionality Test")
    print("=" * 40)
    
    tests = [
        ("File Size Limits", test_file_size_limits),
        ("ZIP Validation", test_zip_validation),
        ("ZIP Extraction", test_zip_extraction),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{'✓' if result else '✗'} {test_name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"✗ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 40)
    print("Test Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == '__main__':
    sys.exit(main())
