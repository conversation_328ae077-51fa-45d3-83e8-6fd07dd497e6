#!/usr/bin/env python3
"""
Enhanced NLP Models Installation Script for AIMetaHarvest

This script installs better BERT and spaCy models for improved NLP performance.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔧 {description}")
    print(f"Running: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error: {e.stderr.strip()}")
        return False

def install_requirements():
    """Install updated requirements"""
    print("📦 Installing updated Python packages...")
    
    commands = [
        ("pip install --upgrade pip", "Upgrading pip"),
        ("pip install -r requirements.txt", "Installing requirements"),
        ("pip install --upgrade transformers torch", "Upgrading transformers and torch"),
        ("pip install --upgrade spacy nltk", "Upgrading spaCy and NLTK"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            print(f"⚠️ Warning: {description} failed, but continuing...")

def install_spacy_models():
    """Install enhanced spaCy models"""
    print("\n🧠 Installing enhanced spaCy models...")
    
    # List of models to try installing (from best to fallback)
    models = [
        ("en_core_web_trf", "Transformer-based English model (best accuracy)"),
        ("en_core_web_lg", "Large English model (good accuracy, faster)"),
        ("en_core_web_md", "Medium English model (balanced)"),
        ("en_core_web_sm", "Small English model (fallback)")
    ]
    
    installed_models = []
    
    for model, description in models:
        print(f"\n📥 Attempting to install {model} - {description}")
        if run_command(f"python -m spacy download {model}", f"Installing {model}"):
            installed_models.append(model)
            print(f"✅ {model} installed successfully")
        else:
            print(f"⚠️ Failed to install {model}, trying next model...")
    
    if installed_models:
        print(f"\n🎉 Successfully installed spaCy models: {', '.join(installed_models)}")
        print(f"🏆 Best model available: {installed_models[0]}")
    else:
        print("\n❌ Failed to install any spaCy models")
        return False
    
    return True

def download_nltk_data():
    """Download required NLTK data"""
    print("\n📚 Downloading NLTK data...")
    
    nltk_downloads = [
        "punkt",
        "stopwords", 
        "wordnet",
        "averaged_perceptron_tagger",
        "vader_lexicon"
    ]
    
    for data in nltk_downloads:
        print(f"📥 Downloading NLTK {data}...")
        if run_command(f'python -c "import nltk; nltk.download(\'{data}\', quiet=True)"', f"Downloading NLTK {data}"):
            print(f"✅ NLTK {data} downloaded")
        else:
            print(f"⚠️ Failed to download NLTK {data}")

def test_models():
    """Test if the enhanced models are working"""
    print("\n🧪 Testing enhanced NLP models...")
    
    test_script = '''
import sys
try:
    # Test spaCy models
    import spacy
    models_to_test = ["en_core_web_trf", "en_core_web_lg", "en_core_web_md", "en_core_web_sm"]
    working_models = []
    
    for model in models_to_test:
        try:
            nlp = spacy.load(model)
            doc = nlp("This is a test sentence for NLP processing.")
            working_models.append(model)
            print(f"✅ spaCy {model}: Working ({len(doc.ents)} entities found)")
            break  # Use the first working model
        except OSError:
            continue
    
    if not working_models:
        print("❌ No spaCy models working")
        sys.exit(1)
    
    # Test transformers
    from transformers import AutoTokenizer, AutoModel
    models_to_test = ["roberta-large", "bert-large-uncased", "bert-base-uncased", "distilbert-base-uncased"]
    
    for model in models_to_test:
        try:
            tokenizer = AutoTokenizer.from_pretrained(model)
            model_obj = AutoModel.from_pretrained(model)
            print(f"✅ Transformers {model}: Working")
            break
        except Exception as e:
            print(f"⚠️ Transformers {model}: {str(e)[:50]}...")
            continue
    
    print("🎉 Enhanced NLP models are working!")
    
except Exception as e:
    print(f"❌ Error testing models: {e}")
    sys.exit(1)
'''
    
    if run_command(f'python -c "{test_script}"', "Testing enhanced NLP models"):
        print("✅ All enhanced models are working correctly!")
        return True
    else:
        print("❌ Some models may not be working properly")
        return False

def main():
    """Main installation function"""
    print("🚀 Enhanced NLP Models Installation for AIMetaHarvest")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Install requirements
    install_requirements()
    
    # Install spaCy models
    if not install_spacy_models():
        print("❌ Failed to install spaCy models")
        sys.exit(1)
    
    # Download NLTK data
    download_nltk_data()
    
    # Test models
    test_models()
    
    print("\n" + "=" * 60)
    print("🎉 Enhanced NLP Models Installation Complete!")
    print("\nYour system now has:")
    print("• Better BERT models (RoBERTa-large → BERT-large → BERT-base → DistilBERT)")
    print("• Enhanced spaCy models (Transformer → Large → Medium → Small)")
    print("• Improved NER capabilities")
    print("• Better semantic search performance")
    print("\n🚀 You can now run your application with enhanced NLP capabilities!")
    print("\nNext steps:")
    print("1. Start your application: python run.py")
    print("2. Start Celery worker: python -m celery -A celery_app worker --loglevel=info")
    print("3. Upload datasets to see improved metadata generation!")

if __name__ == "__main__":
    main()
