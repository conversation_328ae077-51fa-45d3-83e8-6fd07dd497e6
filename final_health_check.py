#!/usr/bin/env python3
"""
Final health check for AIMetaHarvest application.
Quick verification that all essential components are working.
"""

import os
import sys
import importlib.util

def test_essential_imports():
    """Test essential imports without heavy NLP loading"""
    print("🔍 Testing Essential Imports...")
    
    essential_modules = [
        ('Flask', 'flask'),
        ('MongoEngine', 'mongoengine'),
        ('Pandas', 'pandas'),
        ('Celery', 'celery'),
        ('ReportLab', 'reportlab'),
        ('Scikit-learn', 'sklearn')
    ]
    
    for name, module in essential_modules:
        try:
            __import__(module)
            print(f"  ✅ {name}")
        except ImportError as e:
            print(f"  ❌ {name}: {e}")
            return False
    
    return True

def test_file_structure():
    """Test that all essential files exist"""
    print("\n📁 Testing File Structure...")
    
    essential_files = [
        'run.py',
        'celery_app.py',
        'requirements.txt',
        'app/__init__.py',
        'app/config.py',
        'app/models/dataset.py',
        'app/routes/main.py',
        'app/services/dataset_service.py',
        'app/utils/file_utils.py'
    ]
    
    missing_files = []
    for file_path in essential_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_directory_structure():
    """Test that all required directories exist"""
    print("\n📂 Testing Directory Structure...")
    
    required_dirs = [
        'app',
        'app/models',
        'app/routes',
        'app/services',
        'app/templates',
        'app/static',
        'app/utils',
        'uploads'
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            print(f"  ✅ {dir_path}/")
        else:
            print(f"  ❌ {dir_path}/")
            missing_dirs.append(dir_path)
    
    return len(missing_dirs) == 0

def test_configuration():
    """Test configuration files"""
    print("\n⚙️ Testing Configuration...")
    
    # Test requirements.txt
    if os.path.exists('requirements.txt'):
        with open('requirements.txt', 'r') as f:
            requirements = f.read()
        
        essential_packages = ['Flask', 'mongoengine', 'pandas', 'celery', 'reportlab']
        missing_packages = []
        
        for package in essential_packages:
            if package.lower() not in requirements.lower():
                missing_packages.append(package)
        
        if missing_packages:
            print(f"  ⚠️ Missing packages in requirements.txt: {missing_packages}")
        else:
            print("  ✅ All essential packages in requirements.txt")
    else:
        print("  ❌ requirements.txt not found")
        return False
    
    # Test Docker files
    docker_files = ['Dockerfile', 'docker-compose.yml']
    for file_name in docker_files:
        if os.path.exists(file_name):
            print(f"  ✅ {file_name}")
        else:
            print(f"  ⚠️ {file_name} not found (optional)")
    
    return True

def test_syntax_basic():
    """Test basic syntax of core files"""
    print("\n🔍 Testing Core File Syntax...")
    
    core_files = [
        'run.py',
        'celery_app.py',
        'app/__init__.py',
        'app/config.py'
    ]
    
    syntax_errors = []
    for file_path in core_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                import ast
                ast.parse(content)
                print(f"  ✅ {file_path}")
            except SyntaxError as e:
                print(f"  ❌ {file_path}: Syntax error - {e}")
                syntax_errors.append(file_path)
            except Exception as e:
                print(f"  ⚠️ {file_path}: {e}")
        else:
            print(f"  ❌ {file_path}: File not found")
            syntax_errors.append(file_path)
    
    return len(syntax_errors) == 0

def check_cleanup_status():
    """Check that cleanup was successful"""
    print("\n🧹 Checking Cleanup Status...")
    
    # Files that should be removed
    removed_patterns = [
        'test_*.py',
        'fix_*.py',
        'update_*.py',
        '*_dataset_*.py',
        'dynamic_code_demo.py',
        'simple_pdf_test.py'
    ]
    
    found_old_files = []
    for root, dirs, files in os.walk('.'):
        if 'venv' in root or '__pycache__' in root:
            continue
        
        for file in files:
            for pattern in removed_patterns:
                if pattern.replace('*', '') in file:
                    found_old_files.append(os.path.join(root, file))
    
    if found_old_files:
        print(f"  ⚠️ Found {len(found_old_files)} files that should have been removed")
        for file in found_old_files[:5]:  # Show first 5
            print(f"    - {file}")
        if len(found_old_files) > 5:
            print(f"    ... and {len(found_old_files) - 5} more")
    else:
        print("  ✅ Cleanup successful - no old test/demo files found")
    
    return len(found_old_files) == 0

def main():
    """Run all health checks"""
    print("🚀 AIMetaHarvest Final Health Check")
    print("=" * 60)
    
    tests = [
        ("Essential Imports", test_essential_imports),
        ("File Structure", test_file_structure),
        ("Directory Structure", test_directory_structure),
        ("Configuration", test_configuration),
        ("Core File Syntax", test_syntax_basic),
        ("Cleanup Status", check_cleanup_status)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\n❌ {test_name} test failed!")
        except Exception as e:
            print(f"\n❌ {test_name} test error: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 HEALTH CHECK RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL HEALTH CHECKS PASSED!")
        print("\n✅ AIMetaHarvest is ready for deployment!")
        print("✅ All essential components are working")
        print("✅ Codebase is clean and organized")
        print("✅ No critical issues found")
        
        print(f"\n🚀 To start the application:")
        print("   1. python run.py")
        print("   2. python -m celery -A celery_app worker --loglevel=info")
        print("   3. Open http://127.0.0.1:5001")
        
    else:
        print("❌ Some health checks failed!")
        print("🔧 Please review the issues above before deployment")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
