#!/usr/bin/env python3
"""
Script to properly download and cache RoBERTa and FLAN-T5 Base models for offline use
Fixes the offline loading issues by ensuring proper model caching
"""

import os
import sys
from pathlib import Path

def download_and_cache_models():
    """Download and properly cache models for offline use"""
    print("🔄 Downloading and Caching Models for Offline Use")
    print("=" * 60)
    
    try:
        from transformers import AutoTokenizer, AutoModel, T5Tokenizer, T5ForConditionalGeneration
        import torch
        
        # Models to download and cache
        models_to_cache = [
            {
                'name': 'roberta-large',
                'display_name': 'RoBERTa-Large',
                'type': 'bert',
                'tokenizer_class': AutoTokenizer,
                'model_class': AutoModel
            },
            {
                'name': 'bert-large-uncased',
                'display_name': 'BERT-Large',
                'type': 'bert',
                'tokenizer_class': AutoTokenizer,
                'model_class': AutoModel
            },
            {
                'name': 'bert-base-uncased',
                'display_name': 'BERT-Base',
                'type': 'bert',
                'tokenizer_class': AutoTokenizer,
                'model_class': AutoModel
            },
            {
                'name': 'google/flan-t5-base',
                'display_name': 'FLAN-T5 Base',
                'type': 't5',
                'tokenizer_class': T5Tokenizer,
                'model_class': T5ForConditionalGeneration,
                'tokenizer_kwargs': {'legacy': False}
            },
            {
                'name': 't5-small',
                'display_name': 'T5-Small',
                'type': 't5',
                'tokenizer_class': T5Tokenizer,
                'model_class': T5ForConditionalGeneration
            }
        ]
        
        # Get cache directory
        cache_dir = Path.home() / ".cache" / "huggingface" / "transformers"
        cache_dir.mkdir(parents=True, exist_ok=True)
        print(f"📁 Using cache directory: {cache_dir}")
        
        successful_downloads = 0
        
        for model_info in models_to_cache:
            model_name = model_info['name']
            display_name = model_info['display_name']
            tokenizer_class = model_info['tokenizer_class']
            model_class = model_info['model_class']
            tokenizer_kwargs = model_info.get('tokenizer_kwargs', {})
            
            print(f"\n🔄 Processing {display_name} ({model_name})...")
            
            try:
                # Download tokenizer
                print(f"   📥 Downloading tokenizer...")
                tokenizer = tokenizer_class.from_pretrained(
                    model_name,
                    cache_dir=str(cache_dir),
                    local_files_only=False,
                    **tokenizer_kwargs
                )
                print(f"   ✅ Tokenizer downloaded and cached")
                
                # Download model
                print(f"   📥 Downloading model (this may take several minutes)...")
                model = model_class.from_pretrained(
                    model_name,
                    cache_dir=str(cache_dir),
                    local_files_only=False
                )
                print(f"   ✅ Model downloaded and cached")
                
                # Test offline loading
                print(f"   🔍 Testing offline access...")
                try:
                    offline_tokenizer = tokenizer_class.from_pretrained(
                        model_name,
                        cache_dir=str(cache_dir),
                        local_files_only=True,
                        **tokenizer_kwargs
                    )
                    offline_model = model_class.from_pretrained(
                        model_name,
                        cache_dir=str(cache_dir),
                        local_files_only=True
                    )
                    print(f"   ✅ Offline access verified")
                    
                    # Test functionality
                    if model_info['type'] == 't5':
                        test_input = "summarize: This is a test dataset for offline functionality verification."
                        inputs = offline_tokenizer.encode(test_input, return_tensors="pt", max_length=512, truncation=True)
                        
                        with torch.no_grad():
                            outputs = offline_model.generate(
                                inputs,
                                max_length=50,
                                min_length=10,
                                do_sample=False
                            )
                        
                        result = offline_tokenizer.decode(outputs[0], skip_special_tokens=True)
                        print(f"   🧪 Test generation: {result[:50]}...")
                    
                    elif model_info['type'] == 'bert':
                        test_text = "This is a test sentence for BERT model verification."
                        inputs = offline_tokenizer(test_text, return_tensors="pt", max_length=512, truncation=True)
                        
                        with torch.no_grad():
                            outputs = offline_model(**inputs)
                        
                        print(f"   🧪 Test embedding shape: {outputs.last_hidden_state.shape}")
                    
                    print(f"   🎉 {display_name} is fully functional offline!")
                    successful_downloads += 1
                    
                except Exception as offline_e:
                    print(f"   ⚠️ Offline verification failed: {offline_e}")
                    print(f"   📝 Model downloaded but may need internet for first use")
                
            except Exception as e:
                print(f"   ❌ Failed to download {display_name}: {e}")
                continue
        
        # Check cache size
        try:
            cache_files = list(cache_dir.rglob("*"))
            cache_size = sum(f.stat().st_size for f in cache_files if f.is_file())
            cache_size_mb = cache_size / (1024 * 1024)
            print(f"\n📊 Cache Statistics:")
            print(f"   Total files: {len(cache_files)}")
            print(f"   Total size: {cache_size_mb:.1f} MB")
        except Exception as e:
            print(f"⚠️ Could not calculate cache size: {e}")
        
        print(f"\n📈 Download Summary:")
        print(f"   Successfully cached: {successful_downloads}/{len(models_to_cache)} models")
        
        if successful_downloads >= 3:
            print("\n🎉 Model caching completed successfully!")
            print("✅ Models are now available for offline use")
            return True
        else:
            print("\n⚠️ Some models failed to cache properly")
            print("🔄 You may need to run this script again or check your internet connection")
            return False
            
    except ImportError as e:
        print(f"❌ Required packages not installed: {e}")
        print("Please run: pip install transformers torch")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_offline_functionality():
    """Test if models work properly offline"""
    print("\n🔌 Testing Offline Functionality")
    print("=" * 40)
    
    # Temporarily set offline mode
    os.environ["HF_HUB_OFFLINE"] = "1"
    os.environ["TRANSFORMERS_OFFLINE"] = "1"
    
    try:
        from transformers import AutoTokenizer, AutoModel, T5Tokenizer, T5ForConditionalGeneration
        import torch
        
        # Test models
        test_results = {}
        
        # Test RoBERTa
        try:
            print("🔄 Testing RoBERTa offline...")
            tokenizer = AutoTokenizer.from_pretrained("roberta-large", local_files_only=True)
            model = AutoModel.from_pretrained("roberta-large", local_files_only=True)
            
            # Quick test
            inputs = tokenizer("Test sentence", return_tensors="pt")
            with torch.no_grad():
                outputs = model(**inputs)
            
            print("✅ RoBERTa working offline")
            test_results['roberta'] = True
        except Exception as e:
            print(f"❌ RoBERTa offline failed: {e}")
            test_results['roberta'] = False
        
        # Test FLAN-T5 Base
        try:
            print("🔄 Testing FLAN-T5 Base offline...")
            tokenizer = T5Tokenizer.from_pretrained("google/flan-t5-base", local_files_only=True, legacy=False)
            model = T5ForConditionalGeneration.from_pretrained("google/flan-t5-base", local_files_only=True)
            
            # Quick test
            inputs = tokenizer.encode("summarize: Test dataset", return_tensors="pt")
            with torch.no_grad():
                outputs = model.generate(inputs, max_length=20)
            result = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            print(f"✅ FLAN-T5 Base working offline: {result}")
            test_results['flan_t5'] = True
        except Exception as e:
            print(f"❌ FLAN-T5 Base offline failed: {e}")
            test_results['flan_t5'] = False
        
        # Test T5-small fallback
        try:
            print("🔄 Testing T5-small offline...")
            tokenizer = T5Tokenizer.from_pretrained("t5-small", local_files_only=True)
            model = T5ForConditionalGeneration.from_pretrained("t5-small", local_files_only=True)
            
            # Quick test
            inputs = tokenizer.encode("summarize: Test dataset", return_tensors="pt")
            with torch.no_grad():
                outputs = model.generate(inputs, max_length=20)
            result = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            print(f"✅ T5-small working offline: {result}")
            test_results['t5_small'] = True
        except Exception as e:
            print(f"❌ T5-small offline failed: {e}")
            test_results['t5_small'] = False
        
        working_models = sum(test_results.values())
        total_models = len(test_results)
        
        print(f"\n📊 Offline Test Results: {working_models}/{total_models} models working")
        
        if working_models >= 2:
            print("🎉 Offline functionality is working!")
            return True
        else:
            print("⚠️ Limited offline functionality")
            return False
            
    except Exception as e:
        print(f"❌ Offline test failed: {e}")
        return False
    finally:
        # Restore online mode
        if "HF_HUB_OFFLINE" in os.environ:
            del os.environ["HF_HUB_OFFLINE"]
        if "TRANSFORMERS_OFFLINE" in os.environ:
            del os.environ["TRANSFORMERS_OFFLINE"]

def main():
    """Main function"""
    print("🚀 Model Offline Caching and Testing Script")
    print("=" * 60)
    
    # Step 1: Download and cache models
    cache_success = download_and_cache_models()
    
    # Step 2: Test offline functionality
    offline_success = test_offline_functionality()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    
    print(f"📥 Model Caching: {'✅ SUCCESS' if cache_success else '❌ FAILED'}")
    print(f"🔌 Offline Testing: {'✅ SUCCESS' if offline_success else '❌ FAILED'}")
    
    if cache_success and offline_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Models are properly cached and working offline")
        print("\n📋 What's working:")
        print("• RoBERTa and BERT models for embeddings")
        print("• FLAN-T5 Base for advanced description generation")
        print("• T5-small as reliable fallback")
        print("• Complete offline functionality")
        
        print("\n🚀 Your app is now ready with offline model support!")
        
    elif cache_success:
        print("\n✅ MODELS CACHED SUCCESSFULLY")
        print("⚠️ Some offline functionality may be limited")
        print("🔄 Try running the app - it should work better now")
        
    else:
        print("\n⚠️ SOME ISSUES ENCOUNTERED")
        print("🔄 You may need to:")
        print("• Check your internet connection")
        print("• Ensure sufficient disk space")
        print("• Run this script again")
    
    return cache_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
