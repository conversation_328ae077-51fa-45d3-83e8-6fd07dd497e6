{"export_info": {"export_format": "JSON", "export_date": "2025-06-28T12:26:27.672303", "generated_by": "AI-Powered Metadata Harvesting System", "version": "2.0"}, "basic_info": {"id": "6859721bbbcc389ab615219e", "title": "World Education Data", "source": "File Upload", "category": "Education", "data_type": "", "format": "csv", "file_size": null, "license": null, "created_at": "2025-06-23T15:26:19.089000", "updated_at": "2025-06-28T11:21:10.395000", "source_url": "", "persistent_identifier": null, "encoding_format": null, "status": "completed"}, "description": {"main_description": "This dataset, titled 'World Education Data', represents a comprehensive collection of structured data designed for advanced analytical and research applications. The dataset encompasses 5,892 records systematically organized across 11 distinct fields, providing a robust foundation for statistical analysis and data mining operations. Primary fields include: country, country_code, year, gov_exp_pct_gdp, lit_rate_adult_pct, pri_comp_rate_pct, along with 5 additional variables providing comprehensive data coverage. The dataset incorporates textual data elements enabling qualitative analysis and natural language processing applications. Numerical data components support statistical modeling, mathematical analysis, and quantitative research methodologies. The dataset encompasses key concepts and topics including: afghanistan, afg, world, education, data, indicating its relevance for domain-specific research and analysis. This dataset is particularly well-suited for large-scale statistical analysis and machine learning model development, and multivariate analysis and feature engineering. The structured nature of this dataset, combined with its comprehensive field coverage, makes it a valuable resource for researchers, analysts, and data scientists seeking to derive meaningful insights through rigorous analytical methodologies.", "structured_description": null, "auto_generated_description": null}, "data_statistics": {"record_count": 5892, "field_count": 11, "field_names": ["country", " country_code", " year", " gov_exp_pct_gdp", " lit_rate_adult_pct", " pri_comp_rate_pct", " pupil_teacher_primary", " pupil_teacher_secondary", " school_enrol_primary_pct", " school_enrol_secondary_pct", " school_enrol_tertiary_pct"], "data_types": ["object", " int64", " float64"], "data_distribution_types": null}, "metadata_fields": {"tags": ["academic", " afg", " afghanistan", " analysis", " column", " comprehensive", " contains", " country", " data", " dataset", " descriptive", " education", " educational", " field", " geographic", " information", " metrics", " numerical", " qualitative", " quantitative"], "keywords": ["[\"data\"", " \"afghanistan\"", " \"contains\"", " \"country\"", " \"dataset\"", " \"field\"", " \"column\"", " \"information\"", " \"afg\"", " \"analysis\"", " \"this\"", " \"research\"", " \"world\"", " \"education\"", " \"year\"", " \"country code\"", " \"gov exp pct gdp\"", " \"lit rate adult pct\"", " \"pri comp rate pct\"", " \"pupil teacher primary\"]"], "use_cases": ["\"educational research", " learning analytics", " curriculum development\""], "entities": null, "sentiment": null}, "quality_assessment": {"overall_quality_score": 72.79837662337663, "completeness": 83.63636363636364, "consistency": 56.0, "accuracy": 79.0, "timeliness": 75.5, "conformity": 60.92857142857143, "integrity": 81.0, "issues": ["Inconsistent field definition for country", "Inconsistent field definition for country_code", "Inconsistent field definition for year", "Inconsistent field definition for school_enrol_primary_pct", "Inconsistent field definition for school_enrol_secondary_pct"], "recommendations": ["Consider adding these fields to enhance completeness: data_type, publisher, license", "Specify update frequency to improve timeliness assessment", "Assign a persistent identifier to improve findability", "Ensure the dataset is indexed in searchable resources", "Provide a standard access URL to improve accessibility", "Use FAIR vocabularies to improve interoperability", "Include qualified references to related datasets", "Specify a clear license to improve reusability", "Add detailed provenance information", "Add education data standards compliance information"], "assessment_date": "2025-06-23T15:27:28.345000"}, "fair_compliance": {"overall_score": 88.0, "is_compliant": true, "findable_score": 80.0, "accessible_score": 100.0, "interoperable_score": 84.0, "reusable_score": 88.0}, "standards_compliance": {"schema_org_compliant": false, "dublin_core": {}, "dcat_metadata": {}, "json_ld": {}}, "visualizations": {"quality_metrics": {"type": "quality_metrics", "data": {"overall_score": 72.79837662337663, "completeness": 83.63636363636364, "consistency": 56.0, "accuracy": 79.0, "fair_compliant": false}, "chart_config": {"type": "radar", "title": "Data Quality Assessment", "description": "Comprehensive quality metrics for the dataset"}}, "data_overview": {"type": "data_overview", "data": {"record_count": 5892, "field_count": 11, "data_size": "5892 rows × 11 columns", "estimated_size_mb": 0.49}, "chart_config": {"type": "info_cards", "title": "Dataset Overview", "description": "Basic statistics about the dataset structure"}}, "field_analysis": {"type": "field_analysis", "data": {"type_distribution": {"object": 2, "int64": 1, "float64": 2}, "numeric_fields": 0, "text_fields": 0, "categorical_fields": 0, "datetime_fields": 0}, "chart_config": {"type": "pie", "title": "Field Type Distribution", "description": "Distribution of data types across dataset fields"}}, "keyword_cloud": {"type": "keyword_cloud", "data": {"keywords": [{"text": "data", "weight": 21}, {"text": "afghanistan", "weight": 20}, {"text": "contains", "weight": 19}, {"text": "country", "weight": 18}, {"text": "dataset", "weight": 17}, {"text": "field", "weight": 16}, {"text": "column", "weight": 15}, {"text": "information", "weight": 14}, {"text": "afg", "weight": 13}, {"text": "analysis", "weight": 12}, {"text": "this", "weight": 11}, {"text": "research", "weight": 10}, {"text": "world", "weight": 9}, {"text": "education", "weight": 8}, {"text": "year", "weight": 7}], "total_keywords": 15}, "chart_config": {"type": "wordcloud", "title": "Content Keywords", "description": "Most relevant keywords extracted from dataset content"}}, "metadata_completeness": {"type": "metadata_completeness", "data": {"completeness_fields": {"title": true, "description": true, "source": true, "category": true, "tags": true, "quality_assessed": true, "schema_defined": false}, "completed_count": 6, "total_count": 7, "completeness_percentage": 85.7}, "chart_config": {"type": "progress_bar", "title": "Metadata Completeness", "description": "85.7% of metadata fields are complete"}}, "data_distribution": {"type": "histogram", "data": {"bins": ["14.3-213.6", "213.6-413.0", "413.0-612.4", "612.4-811.8", "811.8-1011.1", "1011.1-1210.5", "1210.5-1409.9", "1409.9-1609.3", "1609.3-1808.6", "1808.6-2008.0"], "frequencies": [20, 0, 0, 0, 0, 0, 0, 0, 0, 10], "xlabel": "Value Range", "total_values": 30}, "chart_config": {"type": "histogram", "title": "Data Distribution", "description": "Distribution of 30 numeric values"}}, "correlation_analysis": {"type": "heatmap", "data": {"matrix": [[1.0, 0.8606653269866571, 0.7439346139792408], [0.8606653269866572, 1.0, 0.4636390690385478], [0.7439346139792407, 0.46363906903854785, 1.0]], "labels": {"x": ["year", "school_enrol_primary_pct", "school_enrol_secondary_pct"], "y": ["year", "school_enrol_primary_pct", "school_enrol_secondary_pct"]}, "max": 1.0}, "chart_config": {"type": "heatmap", "title": "Correlation Analysis", "description": "Correlation matrix for 3 numeric fields"}}, "trend_analysis": {"type": "line", "data": {"labels": ["Record 0", "Record 10", "Record 20", "Record 30", "Record 40", "Record 50", "Record 60", "Record 70", "Record 80", "Record 90"], "values": [87.0, 83.1, 87.2, 83.3, 87.4, 83.5, 87.6, 83.7, 87.8, 83.9], "x": [0, 10, 20, 30, 40, 50, 60, 70, 80, 90], "y": [87.0, 83.1, 87.2, 83.3, 87.4, 83.5, 87.6, 83.7, 87.8, 83.9]}, "chart_config": {"type": "line", "title": "Data Quality Trend", "description": "Quality trend across 10 sample points"}}, "generated_at": "2025-06-28T12:21:10.367237", "visualization_version": "3.0"}, "health_report": null, "ai_compliance": null, "processing_metadata": null, "python_examples": {"basic_loading": "# Load World Education Data dataset\nimport pandas as pd\nimport numpy as np\n\n# Load the dataset\ndf = pd.read_csv('world_education_data.csv')\n\n# Basic information\nprint(f\"Dataset shape: {df.shape}\")\nprint(f\"Columns: {list(df.columns)}\")\nprint(df.head())", "data_exploration": "# Data exploration for World Education Data\nimport matplotlib.pyplot as plt\nimport seaborn as sns\n\n# Dataset overview\nprint(df.info())\nprint(df.describe())\n\n# Check for missing values\nprint(df.isnull().sum())\n\n# Basic visualizations\nplt.figure(figsize=(10, 6))\n# Analyze country_code\n# Analyze year"}, "nlp_analysis": {"entities": null, "sentiment": null, "summary": null, "language_detected": "en", "content_analysis": null}, "technical_metadata": {"file_format": "csv", "encoding": null, "delimiter": null, "compression": null, "checksum": null, "file_path": "C:\\Users\\<USER>\\Desktop\\AIMetaHarvest\\app\\uploads\\20250623_162619_world-education-data.csv", "upload_date": "2025-06-23T15:26:19.089000", "last_modified": "2025-06-28T11:21:10.395000"}, "data_quality_metrics": {"completeness_score": null, "consistency_score": null, "accuracy_score": null, "validity_score": null, "uniqueness_score": null, "missing_values_count": null, "duplicate_records_count": null}, "statistical_summary": null, "data_lineage": {"source_system": null, "collection_method": null, "processing_history": null, "transformation_applied": null, "validation_rules": null}}