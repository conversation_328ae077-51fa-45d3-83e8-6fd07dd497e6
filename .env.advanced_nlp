
# Advanced NLP Models Configuration
# Add these to your .env file or environment variables

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o  # or gpt-4-turbo, gpt-3.5-turbo

# Anthropic Configuration  
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022  # or claude-3-opus-20240229

# Google Gemini Configuration
GOOGLE_API_KEY=your_google_api_key_here
GEMINI_MODEL=gemini-1.5-pro  # or gemini-1.5-flash for faster responses

# Free AI Models Configuration
MISTRAL_API_KEY=your_mistral_api_key_here
GROQ_API_KEY=your_groq_api_key_here
TOGETHER_API_KEY=your_together_api_key_here
HF_API_KEY=your_huggingface_api_key_here

# Local Model Configuration
USE_LOCAL_MODELS=false  # Set to true to use local LLaMA models
LOCAL_MODEL_PATH=./models/  # Path to store local models
LOCAL_MODEL_NAME=meta-llama/Llama-2-7b-chat-hf  # Model to download

# Model Preferences (priority order)
PRIMARY_MODEL=mistral
FALLBACK_MODEL=groq
TERTIARY_MODEL=huggingface

MISTRAL_API_KEY=l9GKHibNgRJrrG3gJX1YgFUmBTNb7Bd8
GROQ_API_KEY=********************************************************
TOGETHER_API_KEY=your_together_key_here
HF_API_KEY=*************************************
