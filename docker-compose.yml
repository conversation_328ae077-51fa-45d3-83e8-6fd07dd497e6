version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: aimetaharvest-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_DATABASE: dataset_metadata_manager
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    networks:
      - aimetaharvest-network

  # AIMetaHarvest Application
  app:
    build: .
    container_name: aimetaharvest-app
    restart: unless-stopped
    ports:
      - "5001:5001"
    environment:
      - FLASK_ENV=production
      - MONGODB_HOST=mongodb
      - MONGODB_PORT=27017
      - MONGODB_DB=dataset_metadata_manager
      - SECRET_KEY=docker-secret-key-change-in-production
      - UPLOAD_FOLDER=uploads
      - ENABLE_SEMANTIC_SEARCH=true
      - DEBUG=false
    volumes:
      - ./uploads:/app/uploads
      - ./app/cache:/app/app/cache
    depends_on:
      - mongodb
    networks:
      - aimetaharvest-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  mongodb_data:
    driver: local

networks:
  aimetaharvest-network:
    driver: bridge
