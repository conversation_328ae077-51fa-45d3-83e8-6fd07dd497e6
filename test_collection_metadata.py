#!/usr/bin/env python3
"""
Test script for Collection Metadata Service functionality.
This script tests the new collection-level metadata generation features.
"""

import os
import sys
import tempfile
import json
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def create_mock_dataset(title, description, category, data_type, record_count=100, field_count=5):
    """Create a mock dataset object for testing."""
    class MockDataset:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
            self.created_at = datetime.now()
            self.id = f"test_{title.lower().replace(' ', '_')}"
    
    return MockDataset(
        title=title,
        description=description,
        category=category,
        data_type=data_type,
        record_count=record_count,
        field_count=field_count,
        field_names=','.join([f'field_{i}' for i in range(field_count)]),
        format='csv',
        source='Test Source',
        tags='test,data,sample'
    )

def test_collection_metadata_generation():
    """Test collection metadata generation with mock datasets."""
    print("Testing Collection Metadata Generation...")
    
    try:
        from app.services.collection_metadata_service import CollectionMetadataService
        
        # Create mock collection dataset
        collection_dataset = create_mock_dataset(
            title="Business Analytics Collection",
            description="Collection of business-related datasets",
            category="business",
            data_type="collection",
            record_count=0,
            field_count=0
        )
        collection_dataset.is_collection = True
        collection_dataset.collection_id = "test_collection_123"
        
        # Create mock individual datasets
        individual_datasets = [
            create_mock_dataset(
                title="Sales Data 2023",
                description="Monthly sales data for 2023",
                category="business",
                data_type="tabular",
                record_count=1200,
                field_count=8
            ),
            create_mock_dataset(
                title="Customer Demographics",
                description="Customer demographic information",
                category="business", 
                data_type="tabular",
                record_count=5000,
                field_count=12
            ),
            create_mock_dataset(
                title="Product Inventory",
                description="Current product inventory levels",
                category="business",
                data_type="tabular", 
                record_count=800,
                field_count=6
            )
        ]
        
        # Initialize collection metadata service
        service = CollectionMetadataService()
        
        # Generate collection metadata
        collection_metadata = service.generate_collection_metadata(
            collection_dataset, individual_datasets
        )
        
        # Verify metadata structure
        print("✓ Collection metadata generated successfully")
        print(f"  - Collection info: {bool(collection_metadata.get('collection_info'))}")
        print(f"  - Description: {bool(collection_metadata.get('description'))}")
        print(f"  - Aggregated statistics: {bool(collection_metadata.get('aggregated_statistics'))}")
        print(f"  - Cross-dataset relationships: {bool(collection_metadata.get('cross_dataset_relationships'))}")
        print(f"  - Collection use cases: {bool(collection_metadata.get('collection_use_cases'))}")
        print(f"  - Quality summary: {bool(collection_metadata.get('quality_summary'))}")
        
        # Test specific components
        collection_info = collection_metadata.get('collection_info', {})
        print(f"\nCollection Info:")
        print(f"  - Total datasets: {collection_info.get('total_datasets')}")
        print(f"  - Collection type: {collection_info.get('collection_type')}")
        
        description = collection_metadata.get('description', {})
        if description.get('overview'):
            print(f"\nGenerated Description:")
            print(f"  - Overview: {description['overview']['text'][:100]}...")
            print(f"  - Type: {description['overview']['type']}")
        
        stats = collection_metadata.get('aggregated_statistics', {})
        if stats:
            print(f"\nAggregated Statistics:")
            print(f"  - Total records: {stats.get('total_records')}")
            print(f"  - Total fields: {stats.get('total_fields')}")
            print(f"  - File formats: {stats.get('file_formats')}")
        
        use_cases = collection_metadata.get('collection_use_cases', [])
        if use_cases:
            print(f"\nGenerated Use Cases ({len(use_cases)}):")
            for i, use_case in enumerate(use_cases[:3]):
                print(f"  {i+1}. {use_case}")
        
        quality = collection_metadata.get('quality_summary', {})
        if quality:
            print(f"\nQuality Assessment:")
            print(f"  - Average quality: {quality.get('average_quality_score', 0):.1f}%")
            print(f"  - Overall assessment: {quality.get('overall_assessment')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing collection metadata generation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_domain_identification():
    """Test domain identification functionality."""
    print("\nTesting Domain Identification...")
    
    try:
        from app.services.collection_metadata_service import CollectionMetadataService
        
        service = CollectionMetadataService()
        
        # Test different domain patterns
        test_cases = [
            (['business', 'sales', 'revenue'], ['tabular'], 'business'),
            (['research', 'study', 'analysis'], ['text'], 'research'),
            (['patient', 'medical', 'health'], ['tabular'], 'healthcare'),
            (['student', 'course', 'grade'], ['tabular'], 'education'),
        ]
        
        for categories, data_types, expected_domain in test_cases:
            identified_domain = service._identify_primary_domain(categories, data_types)
            print(f"  - Categories: {categories} → Domain: {identified_domain} (expected: {expected_domain})")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing domain identification: {e}")
        return False

def test_thematic_coherence():
    """Test thematic coherence assessment."""
    print("\nTesting Thematic Coherence Assessment...")
    
    try:
        from app.services.collection_metadata_service import CollectionMetadataService
        
        service = CollectionMetadataService()
        
        # Test coherence with similar categories
        coherent_categories = ['business', 'business', 'business']
        coherent_types = ['tabular', 'tabular', 'tabular']
        coherence_score = service._assess_thematic_coherence(coherent_categories, coherent_types)
        print(f"  - Coherent collection score: {coherence_score:.2f}")
        
        # Test coherence with diverse categories
        diverse_categories = ['business', 'research', 'healthcare']
        diverse_types = ['tabular', 'text', 'time-series']
        diversity_score = service._assess_thematic_coherence(diverse_categories, diverse_types)
        print(f"  - Diverse collection score: {diversity_score:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing thematic coherence: {e}")
        return False

def test_collection_type_determination():
    """Test collection type determination."""
    print("\nTesting Collection Type Determination...")
    
    try:
        from app.services.collection_metadata_service import CollectionMetadataService
        
        service = CollectionMetadataService()
        
        # Test different collection scenarios
        test_scenarios = [
            {
                'statistics': {
                    'file_formats': {'csv': 3},
                    'sources': {'source1': 3}
                },
                'content': {
                    'thematic_coherence': 0.9,
                    'data_diversity': 1
                },
                'expected': 'thematic'
            },
            {
                'statistics': {
                    'file_formats': {'csv': 1, 'json': 1, 'xml': 1},
                    'sources': {'source1': 1, 'source2': 1, 'source3': 1}
                },
                'content': {
                    'thematic_coherence': 0.3,
                    'data_diversity': 4
                },
                'expected': 'diverse'
            }
        ]
        
        for scenario in test_scenarios:
            collection_type = service._determine_collection_type(scenario)
            print(f"  - Scenario → Type: {collection_type} (expected: {scenario['expected']})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing collection type determination: {e}")
        return False

def main():
    """Run all collection metadata tests."""
    print("Collection Metadata Service Test Suite")
    print("=" * 50)
    
    tests = [
        ("Collection Metadata Generation", test_collection_metadata_generation),
        ("Domain Identification", test_domain_identification),
        ("Thematic Coherence", test_thematic_coherence),
        ("Collection Type Determination", test_collection_type_determination),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{'✓' if result else '✗'} {test_name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"✗ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All collection metadata tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == '__main__':
    sys.exit(main())
