#!/usr/bin/env python3
"""
Installation script for FLAN-T5 Base model and Free AI models integration
Upgrades the existing T5-small to FLAN-T5 Base and adds Mistral/Groq support
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"stdout: {e.stdout}")
        if e.stderr:
            print(f"stderr: {e.stderr}")
        return False

def install_free_ai_dependencies():
    """Install Free AI model dependencies"""
    print("\n📦 Installing Free AI Model Dependencies...")
    
    commands = [
        ("pip install --upgrade mistralai==0.4.2", "Installing Mistral AI client"),
        ("pip install --upgrade groq==0.4.1", "Installing Groq client"),
        ("pip install --upgrade requests==2.31.0", "Installing/upgrading requests"),
        ("pip install --upgrade httpx==0.25.0", "Installing/upgrading httpx"),
    ]
    
    success_count = 0
    for command, description in commands:
        if run_command(command, description):
            success_count += 1
        else:
            print(f"⚠️ Warning: {description} failed, but continuing...")
    
    print(f"✅ Installed {success_count}/{len(commands)} Free AI dependencies")
    return success_count > 0

def download_flan_t5_base():
    """Download and verify FLAN-T5 Base model for offline use"""
    print("\n📥 Downloading FLAN-T5 Base Model for Offline Use...")

    download_script = '''
import sys
import os
from pathlib import Path

try:
    from transformers import T5Tokenizer, T5ForConditionalGeneration
    import torch

    print("🔄 Downloading FLAN-T5 Base model for offline storage...")

    # Create local models directory
    models_dir = Path.home() / ".cache" / "huggingface" / "transformers"
    models_dir.mkdir(parents=True, exist_ok=True)
    print(f"📁 Using cache directory: {models_dir}")

    # Download tokenizer with offline storage
    print("📥 Downloading FLAN-T5 Base tokenizer...")
    tokenizer = T5Tokenizer.from_pretrained(
        "google/flan-t5-base",
        cache_dir=str(models_dir),
        local_files_only=False,
        force_download=False
    )
    print("✅ FLAN-T5 Base tokenizer downloaded and cached")

    # Download model with offline storage
    print("📥 Downloading FLAN-T5 Base model (this may take 5-10 minutes)...")
    print("   Model size: ~990MB - Please be patient...")
    model = T5ForConditionalGeneration.from_pretrained(
        "google/flan-t5-base",
        cache_dir=str(models_dir),
        local_files_only=False,
        force_download=False,
        torch_dtype=torch.float32
    )
    print("✅ FLAN-T5 Base model downloaded and cached")

    # Verify offline access
    print("🔍 Verifying offline access...")
    try:
        offline_tokenizer = T5Tokenizer.from_pretrained(
            "google/flan-t5-base",
            local_files_only=True,
            cache_dir=str(models_dir)
        )
        offline_model = T5ForConditionalGeneration.from_pretrained(
            "google/flan-t5-base",
            local_files_only=True,
            cache_dir=str(models_dir)
        )
        print("✅ Offline access verified - model can work without internet")
    except Exception as offline_e:
        print(f"⚠️ Offline verification failed: {offline_e}")
        print("   Model downloaded but may need internet for first use")

    # Test the model functionality
    print("🧪 Testing FLAN-T5 Base model functionality...")
    test_inputs = [
        "summarize: This is a comprehensive dataset containing customer information and sales data for business analysis and research.",
        "describe: A research dataset with 10000 records and 8 fields including demographics and survey responses.",
        "explain: Dataset contains financial transactions with timestamps, amounts, and merchant information for fraud detection."
    ]

    for i, test_input in enumerate(test_inputs, 1):
        try:
            inputs = tokenizer.encode(test_input, return_tensors="pt", max_length=512, truncation=True)

            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    max_length=150,
                    min_length=30,
                    length_penalty=1.5,
                    num_beams=4,
                    early_stopping=True,
                    do_sample=False,
                    repetition_penalty=1.2
                )

            result = tokenizer.decode(outputs[0], skip_special_tokens=True)
            print(f"✅ Test {i} successful: {result[:100]}...")
        except Exception as test_e:
            print(f"⚠️ Test {i} failed: {test_e}")

    # Check model size and location
    cache_files = list(models_dir.rglob("*flan-t5-base*"))
    if cache_files:
        total_size = sum(f.stat().st_size for f in cache_files if f.is_file())
        print(f"📊 Model cached successfully: {len(cache_files)} files, {total_size / (1024*1024):.1f} MB")

    print("🎉 FLAN-T5 Base model is ready for offline use!")

except Exception as e:
    print(f"❌ FLAN-T5 Base download failed: {e}")
    print("🔄 Trying T5-small as fallback...")
    try:
        tokenizer = T5Tokenizer.from_pretrained("t5-small", local_files_only=False)
        model = T5ForConditionalGeneration.from_pretrained("t5-small", local_files_only=False)
        print("✅ T5-small fallback downloaded successfully")

        # Test T5-small
        test_input = "summarize: Test dataset for analysis"
        inputs = tokenizer.encode(test_input, return_tensors="pt", max_length=512, truncation=True)
        with torch.no_grad():
            outputs = model.generate(inputs, max_length=50, min_length=10)
        result = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"✅ T5-small test: {result}")

    except Exception as e2:
        print(f"❌ T5-small fallback also failed: {e2}")
        sys.exit(1)
'''

    return run_command(f'python -c "{download_script}"', "Downloading and caching FLAN-T5 Base model")

def test_free_ai_integration():
    """Test Free AI integration"""
    print("\n🧪 Testing Free AI Integration...")
    
    test_script = '''
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    # Test Free AI Service import
    from app.services.free_ai_service import free_ai_service
    print("✅ Free AI Service imported successfully")
    
    # Check API keys
    mistral_key = os.getenv('MISTRAL_API_KEY')
    groq_key = os.getenv('GROQ_API_KEY')
    
    if mistral_key and mistral_key != 'your_mistral_key_here':
        print("✅ Mistral API key configured")
    else:
        print("⚠️ Mistral API key not configured")
    
    if groq_key and groq_key != 'your_groq_key_here':
        print("✅ Groq API key configured")
    else:
        print("⚠️ Groq API key not configured")
    
    # Test if any free AI is available
    if free_ai_service.is_available():
        print("✅ Free AI models are available")
        
        # Test description generation
        test_data = {
            'title': 'Test Dataset',
            'record_count': 1000,
            'field_names': ['id', 'name', 'value'],
            'category': 'Test'
        }
        
        description = free_ai_service.generate_enhanced_description(test_data)
        if description and len(description) > 50:
            print(f"✅ Free AI description generation working: {len(description)} chars")
        else:
            print("⚠️ Free AI description generation returned short result")
    else:
        print("⚠️ No Free AI models available (check API keys)")
    
    print("🎉 Free AI integration test complete!")
    
except ImportError as e:
    print(f"❌ Import failed: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Free AI test failed: {e}")
    sys.exit(1)
'''
    
    return run_command(f'python -c "{test_script}"', "Testing Free AI integration")

def verify_offline_functionality():
    """Verify FLAN-T5 works completely offline"""
    print("\n🔌 Testing Offline Functionality (No Internet Required)...")

    offline_test_script = '''
import sys
import os
from pathlib import Path

try:
    # Simulate offline environment by temporarily disabling network access for transformers
    os.environ["HF_HUB_OFFLINE"] = "1"
    os.environ["TRANSFORMERS_OFFLINE"] = "1"

    from transformers import T5Tokenizer, T5ForConditionalGeneration
    import torch

    print("🔌 Testing FLAN-T5 Base in offline mode...")

    # Try to load model in offline-only mode
    try:
        tokenizer = T5Tokenizer.from_pretrained(
            "google/flan-t5-base",
            local_files_only=True
        )
        model = T5ForConditionalGeneration.from_pretrained(
            "google/flan-t5-base",
            local_files_only=True
        )
        print("✅ FLAN-T5 Base loaded successfully in offline mode")

        # Test offline generation
        test_prompts = [
            "summarize: Customer database with purchase history and demographics for marketing analysis",
            "describe: Scientific experiment data with measurements and observations from laboratory tests",
            "explain: Financial dataset containing transaction records and account information for audit purposes"
        ]

        for i, prompt in enumerate(test_prompts, 1):
            inputs = tokenizer.encode(prompt, return_tensors="pt", max_length=512, truncation=True)

            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    max_length=120,
                    min_length=25,
                    length_penalty=1.5,
                    num_beams=3,
                    early_stopping=True,
                    do_sample=False
                )

            result = tokenizer.decode(outputs[0], skip_special_tokens=True)
            print(f"✅ Offline test {i}: Generated {len(result)} chars")
            print(f"   Preview: {result[:80]}...")

        print("🎉 FLAN-T5 Base is fully functional offline!")
        return True

    except Exception as offline_e:
        print(f"❌ Offline mode failed: {offline_e}")
        print("   Model may not be properly cached for offline use")
        return False

except Exception as e:
    print(f"❌ Offline test setup failed: {e}")
    return False
finally:
    # Restore online mode
    if "HF_HUB_OFFLINE" in os.environ:
        del os.environ["HF_HUB_OFFLINE"]
    if "TRANSFORMERS_OFFLINE" in os.environ:
        del os.environ["TRANSFORMERS_OFFLINE"]
'''

    return run_command(f'python -c "{offline_test_script}"', "Testing offline functionality")

def test_enhanced_nlp_service():
    """Test enhanced NLP service with new models"""
    print("\n🧪 Testing Enhanced NLP Service...")

    test_script = '''
import sys
try:
    from app.services.nlp_service import nlp_service
    print("✅ NLP Service imported successfully")

    # Test FLAN-T5 availability
    if nlp_service.t5_model and nlp_service.t5_tokenizer:
        print("✅ FLAN-T5 Base model loaded in NLP service")

        # Check model type
        model_name = getattr(nlp_service.t5_model.config, '_name_or_path', 'unknown')
        if 'flan-t5-base' in model_name.lower():
            print("✅ Confirmed: FLAN-T5 Base model is loaded")
        elif 't5-small' in model_name.lower():
            print("⚠️ T5-small is loaded (fallback model)")
        else:
            print(f"ℹ️ T5 model loaded: {model_name}")
    else:
        print("⚠️ FLAN-T5 Base model not available in NLP service")

    # Test enhanced description generation with multiple datasets
    test_datasets = [
        {
            'title': 'Customer Analytics Dataset',
            'record_count': 15000,
            'field_names': ['customer_id', 'age', 'gender', 'purchase_amount', 'product_category', 'region'],
            'data_types': {'customer_id': 'int64', 'age': 'int64', 'gender': 'object', 'purchase_amount': 'float64', 'product_category': 'object', 'region': 'object'},
            'category': 'Business',
            'keywords': ['customer', 'analytics', 'purchase', 'demographics', 'business']
        },
        {
            'title': 'Environmental Monitoring Data',
            'record_count': 8500,
            'field_names': ['timestamp', 'temperature', 'humidity', 'air_quality', 'location'],
            'data_types': {'timestamp': 'datetime64', 'temperature': 'float64', 'humidity': 'float64', 'air_quality': 'int64', 'location': 'object'},
            'category': 'Environmental',
            'keywords': ['environment', 'monitoring', 'sensors', 'climate', 'data']
        },
        {
            'title': 'Medical Research Survey',
            'record_count': 3200,
            'field_names': ['participant_id', 'age', 'diagnosis', 'treatment', 'outcome', 'follow_up_date'],
            'data_types': {'participant_id': 'int64', 'age': 'int64', 'diagnosis': 'object', 'treatment': 'object', 'outcome': 'object', 'follow_up_date': 'datetime64'},
            'category': 'Healthcare',
            'keywords': ['medical', 'research', 'treatment', 'patient', 'clinical']
        }
    ]

    for i, test_data in enumerate(test_datasets, 1):
        print(f"\\n🔄 Testing description generation {i}/3: {test_data['title']}")
        description = nlp_service.generate_enhanced_description(test_data)

        if description and len(description) > 100:
            print(f"✅ Description {i} generated: {len(description)} characters")

            # Check for special characters that should be cleaned
            special_chars = ['#', '*', '`', '@', '$', '%', '^', '&', '+', '=', '[', ']', '{', '}', '|', '\\\\', '<', '>', '~']
            found_special = [char for char in special_chars if char in description]

            if found_special:
                print(f"⚠️ Found special characters: {found_special}")
            else:
                print("✅ Description is clean (no special characters)")

            # Check content quality
            title_mentioned = test_data['title'].lower() in description.lower()
            category_mentioned = test_data['category'].lower() in description.lower()
            record_count_mentioned = str(test_data['record_count']) in description

            quality_score = sum([title_mentioned, category_mentioned, record_count_mentioned])
            print(f"� Content quality: {quality_score}/3 (title: {title_mentioned}, category: {category_mentioned}, count: {record_count_mentioned})")

            print(f"�📄 Preview: {description[:120]}...")
        else:
            print(f"❌ Description {i} generation failed or too short: {description}")

    print("🎉 Enhanced NLP service test complete!")

except Exception as e:
    print(f"❌ Enhanced NLP service test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
'''

    return run_command(f'python -c "{test_script}"', "Testing enhanced NLP service")

def update_env_file():
    """Update .env file with Free AI configuration"""
    print("\n📝 Updating Environment Configuration...")
    
    env_content = """
# Free AI Models for Enhanced Description Generation
MISTRAL_API_KEY=your_mistral_key_here  # Get from: https://console.mistral.ai/
GROQ_API_KEY=your_groq_key_here        # Get from: https://console.groq.com/

# Enhanced Description Configuration
USE_FREE_AI=true
USE_FLAN_T5=true
CLEAN_SPECIAL_CHARACTERS=true
"""
    
    try:
        # Check if .env exists
        if os.path.exists('.env'):
            with open('.env', 'r') as f:
                existing_content = f.read()
            
            # Only add if not already present
            if 'MISTRAL_API_KEY' not in existing_content:
                with open('.env', 'a') as f:
                    f.write(env_content)
                print("✅ Added Free AI configuration to .env file")
            else:
                print("✅ Free AI configuration already exists in .env file")
        else:
            with open('.env', 'w') as f:
                f.write(env_content.strip())
            print("✅ Created .env file with Free AI configuration")
        
        return True
    except Exception as e:
        print(f"⚠️ Failed to update .env file: {e}")
        return False

def test_full_app_functionality():
    """Test complete app functionality with dataset processing"""
    print("\n🔬 Testing Full App Functionality...")

    full_test_script = '''
import sys
import tempfile
import json
import pandas as pd
from pathlib import Path

try:
    # Test dataset creation
    print("📊 Creating test dataset...")
    test_data = {
        'customer_id': range(1, 1001),
        'name': [f'Customer_{i}' for i in range(1, 1001)],
        'age': [25 + (i % 50) for i in range(1000)],
        'purchase_amount': [100.0 + (i * 1.5) for i in range(1000)],
        'product_category': ['Electronics', 'Clothing', 'Books', 'Home'] * 250,
        'region': ['North', 'South', 'East', 'West'] * 250
    }

    df = pd.DataFrame(test_data)

    # Save to temporary CSV file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        df.to_csv(f.name, index=False)
        test_file_path = f.name

    print(f"✅ Test dataset created: {len(df)} records, {len(df.columns)} columns")

    # Test data processing service
    from app.services.processing_service import processing_service
    print("✅ Processing service imported")

    # Create mock dataset object
    class MockDataset:
        def __init__(self):
            self.title = "Customer Purchase Analysis Dataset"
            self.description = "Test dataset for customer analytics"
            self.category = "Business"
            self.file_path = test_file_path
            self.format = "CSV"

    mock_dataset = MockDataset()

    # Test processing
    print("🔄 Testing dataset processing...")
    result = processing_service.process_dataset(mock_dataset)

    if result and 'description' in result:
        description = result['description']
        print(f"✅ Dataset processing successful")
        print(f"📝 Generated description length: {len(description)} characters")

        # Check description quality
        if len(description) > 100:
            print("✅ Description length is adequate")
        else:
            print("⚠️ Description is too short")

        # Check for special characters
        special_chars = ['#', '*', '`', '@', '$', '%', '^', '&']
        found_special = [char for char in special_chars if char in description]

        if found_special:
            print(f"⚠️ Found special characters in description: {found_special}")
        else:
            print("✅ Description is clean (no special characters)")

        # Check content relevance
        title_words = mock_dataset.title.lower().split()
        description_lower = description.lower()
        relevant_words = [word for word in title_words if word in description_lower and len(word) > 3]

        if len(relevant_words) > 0:
            print(f"✅ Description contains relevant terms: {relevant_words}")
        else:
            print("⚠️ Description may not be relevant to dataset title")

        print(f"📄 Description preview: {description[:200]}...")

        # Test other metadata
        if 'keywords' in result and result['keywords']:
            print(f"✅ Keywords generated: {len(result['keywords'])} keywords")

        if 'quality_score' in result:
            print(f"✅ Quality score: {result.get('quality_score', 'N/A')}")

        if 'field_analysis' in result:
            print(f"✅ Field analysis completed: {len(result['field_analysis'])} fields")

    else:
        print("❌ Dataset processing failed")
        return False

    # Cleanup
    Path(test_file_path).unlink(missing_ok=True)
    print("🧹 Cleanup completed")

    print("🎉 Full app functionality test completed successfully!")
    return True

except Exception as e:
    print(f"❌ Full app functionality test failed: {e}")
    import traceback
    traceback.print_exc()
    return False
'''

    return run_command(f'python -c "{full_test_script}"', "Testing full app functionality")

def main():
    """Main installation function"""
    print("🚀 FLAN-T5 Base and Free AI Models Installation & Testing")
    print("=" * 70)

    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        sys.exit(1)

    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")

    # Step 1: Install Free AI dependencies
    print("\n" + "=" * 70)
    print("📦 STEP 1: Installing Dependencies")
    if not install_free_ai_dependencies():
        print("❌ Failed to install Free AI dependencies")
        sys.exit(1)

    # Step 2: Download and cache FLAN-T5 Base model
    print("\n" + "=" * 70)
    print("📥 STEP 2: Downloading FLAN-T5 Base Model")
    flan_t5_ok = download_flan_t5_base()
    if not flan_t5_ok:
        print("⚠️ FLAN-T5 Base download failed, but continuing with tests...")

    # Step 3: Verify offline functionality
    print("\n" + "=" * 70)
    print("🔌 STEP 3: Verifying Offline Functionality")
    offline_ok = verify_offline_functionality()

    # Step 4: Update environment file
    print("\n" + "=" * 70)
    print("📝 STEP 4: Updating Configuration")
    update_env_file()

    # Step 5: Test integrations
    print("\n" + "=" * 70)
    print("🧪 STEP 5: Testing Integrations")

    free_ai_ok = test_free_ai_integration()
    nlp_ok = test_enhanced_nlp_service()

    # Step 6: Test full app functionality
    print("\n" + "=" * 70)
    print("🔬 STEP 6: Testing Full App Functionality")
    app_ok = test_full_app_functionality()

    # Final Summary
    print("\n" + "=" * 70)
    print("📊 INSTALLATION & TESTING SUMMARY")
    print("=" * 70)

    print(f"📦 Dependencies Installation: {'✅ PASS' if True else '❌ FAIL'}")
    print(f"📥 FLAN-T5 Base Download: {'✅ PASS' if flan_t5_ok else '⚠️ PARTIAL'}")
    print(f"🔌 Offline Functionality: {'✅ PASS' if offline_ok else '⚠️ PARTIAL'}")
    print(f"🤖 Free AI Integration: {'✅ PASS' if free_ai_ok else '❌ FAIL'}")
    print(f"🧠 Enhanced NLP Service: {'✅ PASS' if nlp_ok else '❌ FAIL'}")
    print(f"🔬 Full App Functionality: {'✅ PASS' if app_ok else '❌ FAIL'}")

    # Determine overall status
    critical_tests = [nlp_ok, app_ok]
    optional_tests = [flan_t5_ok, offline_ok, free_ai_ok]

    if all(critical_tests):
        print("\n🎉 INSTALLATION SUCCESSFUL!")
        print("✅ Your enhanced description generation system is ready!")

        print("\n🚀 What's Working:")
        if flan_t5_ok and offline_ok:
            print("• FLAN-T5 Base model (offline description generation)")
        if free_ai_ok:
            print("• Free AI models (Mistral/Groq for high-quality descriptions)")
        print("• Enhanced NLP service with fallback system")
        print("• Text cleaning and special character removal")
        print("• Complete dataset processing pipeline")

        print("\n📋 Next Steps:")
        if not free_ai_ok:
            print("1. Add your API keys to the .env file for Free AI models:")
            print("   MISTRAL_API_KEY=your_actual_key_here")
            print("   GROQ_API_KEY=your_actual_key_here")
            print("2. Get free API keys from:")
            print("   • Mistral AI: https://console.mistral.ai/")
            print("   • Groq: https://console.groq.com/")
        print("3. Test your complete setup: python test_free_ai_models.py")
        print("4. Start your application: python run.py")
        print("5. Start background workers: python -m celery -A celery_app worker --loglevel=info")

    else:
        print("\n⚠️ INSTALLATION COMPLETED WITH ISSUES")
        print("Some components may not be working properly.")
        print("Check the test results above and resolve any issues.")

        if not nlp_ok:
            print("\n❌ Critical: Enhanced NLP service failed")
            print("   - Check if transformers library is properly installed")
            print("   - Verify FLAN-T5 model download")

        if not app_ok:
            print("\n❌ Critical: Full app functionality failed")
            print("   - Check if all dependencies are installed")
            print("   - Verify database connection")
            print("   - Check processing service configuration")

    print("\n" + "=" * 70)

if __name__ == "__main__":
    main()
