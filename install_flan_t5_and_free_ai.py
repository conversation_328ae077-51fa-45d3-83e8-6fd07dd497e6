#!/usr/bin/env python3
"""
Installation script for FLAN-T5 Base model and Free AI models integration
Upgrades the existing T5-small to FLAN-T5 Base and adds Mistral/Groq support
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"stdout: {e.stdout}")
        if e.stderr:
            print(f"stderr: {e.stderr}")
        return False

def install_free_ai_dependencies():
    """Install Free AI model dependencies"""
    print("\n📦 Installing Free AI Model Dependencies...")
    
    commands = [
        ("pip install --upgrade mistralai==0.4.2", "Installing Mistral AI client"),
        ("pip install --upgrade groq==0.4.1", "Installing Groq client"),
        ("pip install --upgrade requests==2.31.0", "Installing/upgrading requests"),
        ("pip install --upgrade httpx==0.25.0", "Installing/upgrading httpx"),
    ]
    
    success_count = 0
    for command, description in commands:
        if run_command(command, description):
            success_count += 1
        else:
            print(f"⚠️ Warning: {description} failed, but continuing...")
    
    print(f"✅ Installed {success_count}/{len(commands)} Free AI dependencies")
    return success_count > 0

def download_flan_t5_base():
    """Download FLAN-T5 Base model"""
    print("\n📥 Downloading FLAN-T5 Base Model...")
    
    download_script = '''
import sys
try:
    from transformers import T5Tokenizer, T5ForConditionalGeneration
    import torch
    
    print("🔄 Downloading FLAN-T5 Base model...")
    
    # Download tokenizer
    print("📥 Downloading FLAN-T5 Base tokenizer...")
    tokenizer = T5Tokenizer.from_pretrained("google/flan-t5-base")
    print("✅ FLAN-T5 Base tokenizer downloaded")
    
    # Download model
    print("📥 Downloading FLAN-T5 Base model (this may take a few minutes)...")
    model = T5ForConditionalGeneration.from_pretrained("google/flan-t5-base")
    print("✅ FLAN-T5 Base model downloaded")
    
    # Test the model
    print("🧪 Testing FLAN-T5 Base model...")
    test_input = "summarize: This is a comprehensive dataset containing customer information and sales data for analysis."
    inputs = tokenizer.encode(test_input, return_tensors="pt", max_length=512, truncation=True)
    
    with torch.no_grad():
        outputs = model.generate(
            inputs,
            max_length=100,
            min_length=20,
            length_penalty=1.5,
            num_beams=3,
            early_stopping=True
        )
    
    result = tokenizer.decode(outputs[0], skip_special_tokens=True)
    print(f"✅ FLAN-T5 Base test successful: {result}")
    print("🎉 FLAN-T5 Base model is ready!")
    
except Exception as e:
    print(f"❌ FLAN-T5 Base download failed: {e}")
    print("🔄 Trying T5-small as fallback...")
    try:
        tokenizer = T5Tokenizer.from_pretrained("t5-small")
        model = T5ForConditionalGeneration.from_pretrained("t5-small")
        print("✅ T5-small fallback downloaded successfully")
    except Exception as e2:
        print(f"❌ T5-small fallback also failed: {e2}")
        sys.exit(1)
'''
    
    return run_command(f'python -c "{download_script}"', "Downloading FLAN-T5 Base model")

def test_free_ai_integration():
    """Test Free AI integration"""
    print("\n🧪 Testing Free AI Integration...")
    
    test_script = '''
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    # Test Free AI Service import
    from app.services.free_ai_service import free_ai_service
    print("✅ Free AI Service imported successfully")
    
    # Check API keys
    mistral_key = os.getenv('MISTRAL_API_KEY')
    groq_key = os.getenv('GROQ_API_KEY')
    
    if mistral_key and mistral_key != 'your_mistral_key_here':
        print("✅ Mistral API key configured")
    else:
        print("⚠️ Mistral API key not configured")
    
    if groq_key and groq_key != 'your_groq_key_here':
        print("✅ Groq API key configured")
    else:
        print("⚠️ Groq API key not configured")
    
    # Test if any free AI is available
    if free_ai_service.is_available():
        print("✅ Free AI models are available")
        
        # Test description generation
        test_data = {
            'title': 'Test Dataset',
            'record_count': 1000,
            'field_names': ['id', 'name', 'value'],
            'category': 'Test'
        }
        
        description = free_ai_service.generate_enhanced_description(test_data)
        if description and len(description) > 50:
            print(f"✅ Free AI description generation working: {len(description)} chars")
        else:
            print("⚠️ Free AI description generation returned short result")
    else:
        print("⚠️ No Free AI models available (check API keys)")
    
    print("🎉 Free AI integration test complete!")
    
except ImportError as e:
    print(f"❌ Import failed: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Free AI test failed: {e}")
    sys.exit(1)
'''
    
    return run_command(f'python -c "{test_script}"', "Testing Free AI integration")

def test_enhanced_nlp_service():
    """Test enhanced NLP service with new models"""
    print("\n🧪 Testing Enhanced NLP Service...")
    
    test_script = '''
import sys
try:
    from app.services.nlp_service import nlp_service
    print("✅ NLP Service imported successfully")
    
    # Test FLAN-T5 availability
    if nlp_service.t5_model and nlp_service.t5_tokenizer:
        print("✅ FLAN-T5 Base model loaded in NLP service")
    else:
        print("⚠️ FLAN-T5 Base model not available in NLP service")
    
    # Test enhanced description generation
    test_data = {
        'title': 'Sample Research Dataset',
        'record_count': 5000,
        'field_names': ['participant_id', 'age', 'gender', 'score', 'category'],
        'data_types': {'participant_id': 'int64', 'age': 'int64', 'gender': 'object', 'score': 'float64', 'category': 'object'},
        'category': 'Research',
        'keywords': ['research', 'participants', 'analysis', 'study']
    }
    
    print("🔄 Testing enhanced description generation...")
    description = nlp_service.generate_enhanced_description(test_data)
    
    if description and len(description) > 100:
        print(f"✅ Enhanced description generated: {len(description)} characters")
        
        # Check for special characters
        special_chars = ['#', '*', '`', '@', '$', '%', '^', '&']
        found_special = [char for char in special_chars if char in description]
        
        if found_special:
            print(f"⚠️ Found special characters: {found_special}")
        else:
            print("✅ Description is clean (no special characters)")
        
        print(f"📄 Preview: {description[:150]}...")
    else:
        print(f"⚠️ Enhanced description generation failed or short: {description}")
    
    print("🎉 Enhanced NLP service test complete!")
    
except Exception as e:
    print(f"❌ Enhanced NLP service test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
'''
    
    return run_command(f'python -c "{test_script}"', "Testing enhanced NLP service")

def update_env_file():
    """Update .env file with Free AI configuration"""
    print("\n📝 Updating Environment Configuration...")
    
    env_content = """
# Free AI Models for Enhanced Description Generation
MISTRAL_API_KEY=your_mistral_key_here  # Get from: https://console.mistral.ai/
GROQ_API_KEY=your_groq_key_here        # Get from: https://console.groq.com/

# Enhanced Description Configuration
USE_FREE_AI=true
USE_FLAN_T5=true
CLEAN_SPECIAL_CHARACTERS=true
"""
    
    try:
        # Check if .env exists
        if os.path.exists('.env'):
            with open('.env', 'r') as f:
                existing_content = f.read()
            
            # Only add if not already present
            if 'MISTRAL_API_KEY' not in existing_content:
                with open('.env', 'a') as f:
                    f.write(env_content)
                print("✅ Added Free AI configuration to .env file")
            else:
                print("✅ Free AI configuration already exists in .env file")
        else:
            with open('.env', 'w') as f:
                f.write(env_content.strip())
            print("✅ Created .env file with Free AI configuration")
        
        return True
    except Exception as e:
        print(f"⚠️ Failed to update .env file: {e}")
        return False

def main():
    """Main installation function"""
    print("🚀 FLAN-T5 Base and Free AI Models Installation")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Install Free AI dependencies
    if not install_free_ai_dependencies():
        print("❌ Failed to install Free AI dependencies")
        sys.exit(1)
    
    # Download FLAN-T5 Base model
    if not download_flan_t5_base():
        print("⚠️ FLAN-T5 Base download failed, but continuing...")
    
    # Update environment file
    update_env_file()
    
    # Test Free AI integration
    print("\n" + "=" * 60)
    print("🧪 Running Integration Tests...")
    
    free_ai_ok = test_free_ai_integration()
    nlp_ok = test_enhanced_nlp_service()
    
    # Summary
    print("\n" + "=" * 60)
    print("🎉 Installation Complete!")
    print("=" * 60)
    
    print("\n✅ What's been installed:")
    print("• Free AI model clients (Mistral, Groq)")
    print("• FLAN-T5 Base model for offline description generation")
    print("• Enhanced text cleaning capabilities")
    print("• Updated NLP service with fallback system")
    
    print("\n📋 Next Steps:")
    print("1. Add your API keys to the .env file:")
    print("   MISTRAL_API_KEY=your_actual_key_here")
    print("   GROQ_API_KEY=your_actual_key_here")
    print("2. Get free API keys from:")
    print("   • Mistral AI: https://console.mistral.ai/")
    print("   • Groq: https://console.groq.com/")
    print("3. Test your setup: python test_free_ai_models.py")
    print("4. Start your application: python run.py")
    
    if free_ai_ok and nlp_ok:
        print("\n🎉 All tests passed! Your enhanced description generation is ready!")
    else:
        print("\n⚠️ Some tests failed. Check the output above and configure API keys.")

if __name__ == "__main__":
    main()
