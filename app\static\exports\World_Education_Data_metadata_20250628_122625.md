# World Education Data

## Dataset Metadata Report

**Generated on:** 2025-06-28 12:26:25  
**Dataset ID:** 6859721bbbcc389ab615219e  
**Export Type:** Complete Metadata Export

---

## 📊 Basic Information

| Field | Value |
|-------|-------|
| **Title** | World Education Data |
| **Source** | File Upload |
| **Category** | Education |
| **Data Type** | Not specified |
| **Format** | csv |
| **File Size** | Unknown |
| **License** | Not specified |
| **Created** | 2025-06-23 15:26:19 |
| **Updated** | 2025-06-28 11:21:10 |

## 📝 Description

This dataset, titled 'World Education Data', represents a comprehensive collection of structured data designed for advanced analytical and research applications. The dataset encompasses 5,892 records systematically organized across 11 distinct fields, providing a robust foundation for statistical analysis and data mining operations. Primary fields include: country, country_code, year, gov_exp_pct_gdp, lit_rate_adult_pct, pri_comp_rate_pct, along with 5 additional variables providing comprehensive data coverage. The dataset incorporates textual data elements enabling qualitative analysis and natural language processing applications. Numerical data components support statistical modeling, mathematical analysis, and quantitative research methodologies. The dataset encompasses key concepts and topics including: afghanistan, afg, world, education, data, indicating its relevance for domain-specific research and analysis. This dataset is particularly well-suited for large-scale statistical analysis and machine learning model development, and multivariate analysis and feature engineering. The structured nature of this dataset, combined with its comprehensive field coverage, makes it a valuable resource for researchers, analysts, and data scientists seeking to derive meaningful insights through rigorous analytical methodologies.

## 📈 Data Statistics

| Metric | Value |
|--------|-------|
| **Records** | 5892 |
| **Fields** | 11 |
| **Status** | completed |

## 🏷️ Field Names

1. country
2. country_code
3. year
4. gov_exp_pct_gdp
5. lit_rate_adult_pct
6. pri_comp_rate_pct
7. pupil_teacher_primary
8. pupil_teacher_secondary
9. school_enrol_primary_pct
10. school_enrol_secondary_pct
11. school_enrol_tertiary_pct

## 🔢 Data Types

- object
- int64
- float64

## 🏷️ Tags

- #academic
- #afg
- #afghanistan
- #analysis
- #column
- #comprehensive
- #contains
- #country
- #data
- #dataset
- #descriptive
- #education
- #educational
- #field
- #geographic

## 🔍 Keywords

- ["data"
- "afghanistan"
- "contains"
- "country"
- "dataset"
- "field"
- "column"
- "information"
- "afg"
- "analysis"
- "this"
- "research"
- "world"
- "education"
- "year"

## 💡 Use Cases

- "educational research
- learning analytics
- curriculum development"

## 📊 Quality Assessment

| Metric | Score |
|--------|-------|
| **Overall Quality** | 72.79837662337663% |
| **Completeness** | 83.63636363636364% |
| **Consistency** | 56.0% |
| **Accuracy** | 79.0% |

## 🎯 FAIR Compliance

| Principle | Score | Status |
|-----------|-------|--------|
| **FAIR Score** | 88.0% | ✅ Compliant |
| **Findable** | 80.0 | ✅ |
| **Accessible** | 100.0 | ✅ |
| **Interoperable** | 84.0 | ✅ |
| **Reusable** | 88.0 | ✅ |

## ✅ Standards Compliance

- **Schema.org Compliant:** ❌ No
- **Persistent Identifier:** ❌ Not assigned
- **Encoding Format:** Not specified

## 📋 Available Metadata Standards

- ✅ Dublin Core
- ✅ DCAT (Data Catalog Vocabulary)
- ✅ Schema.org JSON-LD

## 📊 Visualizations


## 🐍 Python Code Examples

### Basic Loading

```python
# Load World Education Data dataset
import pandas as pd
import numpy as np

# Load the dataset
df = pd.read_csv('world_education_data.csv')

# Basic information
print(f"Dataset shape: {df.shape}")
print(f"Columns: {list(df.columns)}")
print(df.head())
```

### Data Exploration

```python
# Data exploration for World Education Data
import matplotlib.pyplot as plt
import seaborn as sns

# Dataset overview
print(df.info())
print(df.describe())

# Check for missing values
print(df.isnull().sum())

# Basic visualizations
plt.figure(figsize=(10, 6))
# Analyze country_code
# Analyze year
```

## 📊 Data Quality Metrics


## ⚙️ Technical Metadata

- **File Format**: CSV
- **Upload Date**: 2025-06-23 15:26:19
- **Last Modified**: 2025-06-28 11:21:10

---

## 📄 Export Information

- **Export Format:** Markdown
- **Generated by:** AI-Powered Metadata Harvesting System
- **Export Date:** 2025-06-28 12:26:25
- **Dataset URL:** N/A

*This metadata export contains comprehensive information about the dataset including quality metrics, FAIR compliance assessment, and standards compliance.*
