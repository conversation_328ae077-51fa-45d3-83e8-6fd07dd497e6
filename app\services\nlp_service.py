"""
NLP Service for dataset processing and analysis.
"""

import re
import json
import os
from collections import Counter
from datetime import datetime
from typing import List, Dict, Any, Optional

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False

try:
    import nltk
    from nltk.corpus import stopwords
    from nltk.tokenize import word_tokenize, sent_tokenize
    from nltk.stem import PorterStemmer
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    import numpy as np
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    from transformers import AutoTokenizer, AutoModel, pipeline, T5Tokenizer, T5ForConditionalGeneration
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

# Import free AI service for enhanced description generation
try:
    from app.services.free_ai_service import free_ai_service
    FREE_AI_AVAILABLE = True
except ImportError:
    FREE_AI_AVAILABLE = False

# Focus on free AI models with FLAN-T5 Base as fallback


class NLPService:
    """Service for NLP processing of dataset content and metadata"""
    
    def __init__(self):
        self.nlp = None
        self.stemmer = None
        self.stop_words = set()
        self.tfidf_vectorizer = None
        self.bert_tokenizer = None
        self.bert_model = None
        self.ner_pipeline = None

        # T5 model for offline description generation
        self.t5_tokenizer = None
        self.t5_model = None

        self._initialize_nlp()
    
    def _initialize_nlp(self):
        """Initialize advanced NLP libraries if available"""
        print("🔧 Initializing Advanced NLP Service with BERT, TF-IDF, and NER...")

        # Initialize spaCy if available - try larger models first
        if SPACY_AVAILABLE:
            try:
                # Try to load the transformer-based model first (most accurate)
                self.nlp = spacy.load("en_core_web_trf")
                print("✅ spaCy Transformer model (en_core_web_trf) loaded successfully")
            except OSError:
                try:
                    # Fallback to large model
                    self.nlp = spacy.load("en_core_web_lg")
                    print("✅ spaCy Large model (en_core_web_lg) loaded successfully")
                except OSError:
                    try:
                        # Fallback to medium model
                        self.nlp = spacy.load("en_core_web_md")
                        print("✅ spaCy Medium model (en_core_web_md) loaded successfully")
                    except OSError:
                        try:
                            # Final fallback to small model
                            self.nlp = spacy.load("en_core_web_sm")
                            print("✅ spaCy Small model (en_core_web_sm) loaded successfully")
                        except OSError:
                            print("⚠️ No spaCy English model found. Install with:")
                            print("   python -m spacy download en_core_web_trf  # Best (transformer-based)")
                            print("   python -m spacy download en_core_web_lg   # Large")
                            print("   python -m spacy download en_core_web_md   # Medium")
                            print("   python -m spacy download en_core_web_sm   # Small")
                            self.nlp = None

        # Initialize NLTK if available
        if NLTK_AVAILABLE:
            try:
                self.stemmer = PorterStemmer()
                self.stop_words = set(stopwords.words('english'))
                print("✅ NLTK initialized successfully")
            except LookupError:
                print("⚠️ NLTK data not found. Download with: nltk.download('stopwords') and nltk.download('punkt')")
                self.stemmer = None
                self.stop_words = set()

        # Initialize TF-IDF Vectorizer
        if SKLEARN_AVAILABLE:
            try:
                self.tfidf_vectorizer = TfidfVectorizer(
                    max_features=500,
                    stop_words='english',
                    ngram_range=(1, 2),
                    min_df=1,  # Minimum document frequency
                    max_df=0.99,  # Maximum document frequency
                    lowercase=True,
                    token_pattern=r'\b[a-zA-Z]{2,}\b'  # Only alphabetic tokens with 2+ chars
                )
                print("✅ TF-IDF Vectorizer initialized successfully")
            except Exception as e:
                print(f"⚠️ TF-IDF initialization failed: {e}")
                self.tfidf_vectorizer = None

        # Initialize BERT model for embeddings - try better models first with offline fallback
        if TRANSFORMERS_AVAILABLE:
            try:
                # Try RoBERTa-large first (most robust) with offline fallback
                try:
                    model_name = "roberta-large"
                    self.bert_tokenizer = AutoTokenizer.from_pretrained(model_name, local_files_only=False)
                    self.bert_model = AutoModel.from_pretrained(model_name, local_files_only=False)
                    print("✅ RoBERTa-Large model loaded successfully")
                except Exception as e:
                    print(f"⚠️ RoBERTa-Large failed (possibly offline): {e}, trying BERT-large...")
                    try:
                        # Fallback to BERT-large
                        model_name = "bert-large-uncased"
                        self.bert_tokenizer = AutoTokenizer.from_pretrained(model_name, local_files_only=False)
                        self.bert_model = AutoModel.from_pretrained(model_name, local_files_only=False)
                        print("✅ BERT-Large model loaded successfully")
                    except Exception as e:
                        print(f"⚠️ BERT-Large failed: {e}, trying BERT-base...")
                        try:
                            # Fallback to BERT-base
                            model_name = "bert-base-uncased"
                            self.bert_tokenizer = AutoTokenizer.from_pretrained(model_name, local_files_only=False)
                            self.bert_model = AutoModel.from_pretrained(model_name, local_files_only=False)
                            print("✅ BERT-Base model loaded successfully")
                        except Exception as e:
                            print(f"⚠️ BERT-Base failed: {e}, trying DistilBERT...")
                            try:
                                # Final fallback to DistilBERT
                                model_name = "distilbert-base-uncased"
                                self.bert_tokenizer = AutoTokenizer.from_pretrained(model_name, local_files_only=False)
                                self.bert_model = AutoModel.from_pretrained(model_name, local_files_only=False)
                                print("✅ DistilBERT model loaded successfully")
                            except Exception as e:
                                print(f"⚠️ All BERT models failed (possibly offline): {e}")
                                print("🔄 Attempting to load cached models...")
                                self._try_load_cached_models()

                # Initialize advanced NER pipeline with better models
                try:
                    # Try the most reliable NER model first
                    self.ner_pipeline = pipeline("ner",
                                                model="dbmdz/bert-large-cased-finetuned-conll03-english",
                                                aggregation_strategy="simple")
                    print("✅ Advanced NER pipeline (BERT-large) initialized successfully")
                except Exception as e:
                    print(f"⚠️ BERT-large NER failed: {e}, trying BERT-base NER...")
                    try:
                        # Fallback to BERT-base NER
                        self.ner_pipeline = pipeline("ner",
                                                    model="dbmdz/bert-base-cased-finetuned-conll03-english",
                                                    aggregation_strategy="simple")
                        print("✅ NER pipeline (BERT-base) initialized successfully")
                    except Exception as e:
                        print(f"⚠️ BERT-base NER failed: {e}, trying DistilBERT NER...")
                        try:
                            # Final fallback to DistilBERT NER
                            self.ner_pipeline = pipeline("ner",
                                                        model="distilbert-base-cased",
                                                        aggregation_strategy="simple")
                            print("✅ NER pipeline (DistilBERT) initialized successfully")
                        except Exception as e:
                            print(f"⚠️ All NER models failed: {e}")
                            self.ner_pipeline = None

            except Exception as e:
                print(f"⚠️ BERT/NER initialization failed: {e}")
                self.bert_tokenizer = None
                self.bert_model = None
                self.ner_pipeline = None

        # Initialize T5 model for offline description generation
        self._initialize_t5_model()

        print("🚀 Advanced NLP Service initialization complete!")



    def _try_load_cached_models(self):
        """Try to load cached/offline models"""
        try:
            # Try to load any cached models from local directories
            import os
            cache_dir = os.path.expanduser("~/.cache/huggingface/transformers")
            if os.path.exists(cache_dir):
                print(f"🔍 Looking for cached models in {cache_dir}")
                # Try to load the smallest available model
                try:
                    model_name = "distilbert-base-uncased"
                    self.bert_tokenizer = AutoTokenizer.from_pretrained(model_name, local_files_only=True)
                    self.bert_model = AutoModel.from_pretrained(model_name, local_files_only=True)
                    print("✅ Loaded cached DistilBERT model")
                except:
                    print("⚠️ No cached models available - running without BERT embeddings")
            else:
                print("⚠️ No cache directory found - running without BERT embeddings")
        except Exception as e:
            print(f"⚠️ Failed to load cached models: {e}")

    def _initialize_t5_model(self):
        """Initialize FLAN-T5 Base model for offline description generation"""
        if not TRANSFORMERS_AVAILABLE:
            print("⚠️ Transformers not available, skipping FLAN-T5 initialization")
            return

        try:
            # Try to load FLAN-T5 Base for better description generation
            model_name = "google/flan-t5-base"
            print(f"🔄 Initializing FLAN-T5 Base model: {model_name}")

            self.t5_tokenizer = T5Tokenizer.from_pretrained(model_name, local_files_only=False)
            self.t5_model = T5ForConditionalGeneration.from_pretrained(model_name, local_files_only=False)
            print("✅ FLAN-T5 Base model loaded successfully for offline description generation")

        except Exception as e:
            print(f"⚠️ FLAN-T5 Base model initialization failed: {e}")
            try:
                # Try to load from cache
                self.t5_tokenizer = T5Tokenizer.from_pretrained(model_name, local_files_only=True)
                self.t5_model = T5ForConditionalGeneration.from_pretrained(model_name, local_files_only=True)
                print("✅ FLAN-T5 Base model loaded from cache")
            except Exception as cache_e:
                print(f"⚠️ FLAN-T5 Base model cache load failed: {cache_e}")
                try:
                    # Fallback to T5-small if FLAN-T5 Base fails
                    fallback_model = "t5-small"
                    print(f"🔄 Falling back to T5-small: {fallback_model}")
                    self.t5_tokenizer = T5Tokenizer.from_pretrained(fallback_model, local_files_only=False)
                    self.t5_model = T5ForConditionalGeneration.from_pretrained(fallback_model, local_files_only=False)
                    print("✅ T5-small fallback model loaded successfully")
                except Exception as fallback_e:
                    print(f"⚠️ All T5 model loading failed: {fallback_e}")
                    self.t5_tokenizer = None
                    self.t5_model = None

    def generate_description_with_t5(self, dataset_info: str) -> str:
        """Generate description using T5 model (offline alternative)"""
        if not self.t5_model or not self.t5_tokenizer:
            return ""

        try:
            # Prepare input for T5
            input_text = f"summarize: {dataset_info}"

            # Tokenize input
            inputs = self.t5_tokenizer.encode(input_text, return_tensors="pt", max_length=512, truncation=True)

            # Generate description
            with torch.no_grad():
                outputs = self.t5_model.generate(
                    inputs,
                    max_length=200,
                    min_length=50,
                    length_penalty=2.0,
                    num_beams=4,
                    early_stopping=True
                )

            # Decode output
            description = self.t5_tokenizer.decode(outputs[0], skip_special_tokens=True)
            print("✅ T5 description generated successfully")
            return description

        except Exception as e:
            print(f"⚠️ T5 description generation failed: {e}")
            return ""

    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """Extract keywords using advanced NLP techniques (TF-IDF, BERT, spaCy)"""
        if not text:
            return []

        # Handle non-string inputs
        if not isinstance(text, str):
            if text is None:
                return []
            text = str(text)

        # Clean text
        text = self._clean_text(text)

        print(f"🔍 Extracting keywords using advanced NLP techniques...")

        # Try TF-IDF first for best results
        if self.tfidf_vectorizer is not None:
            keywords = self._extract_keywords_tfidf(text, max_keywords)
            if keywords:
                print(f"✅ TF-IDF extracted {len(keywords)} keywords")
                return keywords

        # Fallback to spaCy
        if self.nlp:
            keywords = self._extract_keywords_spacy(text, max_keywords)
            print(f"✅ spaCy extracted {len(keywords)} keywords")
            return keywords
        elif NLTK_AVAILABLE:
            keywords = self._extract_keywords_nltk(text, max_keywords)
            print(f"✅ NLTK extracted {len(keywords)} keywords")
            return keywords
        else:
            keywords = self._extract_keywords_simple(text, max_keywords)
            print(f"✅ Simple extraction found {len(keywords)} keywords")
            return keywords
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        # Handle non-string inputs
        if not isinstance(text, str):
            if text is None:
                return ""
            # Convert to string if it's not already
            text = str(text)

        # Remove special characters and normalize whitespace
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip().lower()

    def _extract_keywords_tfidf(self, text: str, max_keywords: int) -> List[str]:
        """Extract keywords using TF-IDF vectorization"""
        try:
            # Clean and prepare text
            if len(text.strip()) < 10:
                print("⚠️ Text too short for TF-IDF analysis")
                return []

            # Create a more flexible TF-IDF vectorizer for this specific text
            from sklearn.feature_extraction.text import TfidfVectorizer

            # Split text into sentences for better TF-IDF analysis
            if NLTK_AVAILABLE:
                sentences = sent_tokenize(text)
            else:
                sentences = [s.strip() for s in text.split('.') if s.strip()]

            # Filter out very short sentences
            sentences = [s for s in sentences if len(s.strip()) > 5]

            # If we have very few sentences, split by other delimiters
            if len(sentences) < 3:
                # Try splitting by other delimiters
                additional_sentences = []
                for delimiter in ['\n', ';', '!', '?']:
                    for sentence in sentences:
                        additional_sentences.extend([s.strip() for s in sentence.split(delimiter) if len(s.strip()) > 5])
                if additional_sentences:
                    sentences = additional_sentences

            # Need at least 2 documents for TF-IDF
            if len(sentences) < 2:
                # Split text into chunks if it's long enough
                if len(text) > 100:
                    chunk_size = max(50, len(text) // 3)
                    sentences = [text[i:i+chunk_size] for i in range(0, len(text), chunk_size)]
                else:
                    # For very short text, create artificial documents
                    words = text.split()
                    if len(words) > 4:
                        mid = len(words) // 2
                        sentences = [' '.join(words[:mid]), ' '.join(words[mid:])]
                    else:
                        sentences = [text, text]  # Last resort

            # Ensure we have valid sentences
            sentences = [s for s in sentences if len(s.strip()) > 3]
            if len(sentences) < 2:
                print("⚠️ Unable to create sufficient documents for TF-IDF, using frequency method")
                return self._extract_keywords_frequency(text, max_keywords)

            # Create a more robust TF-IDF vectorizer with very lenient parameters
            total_words = len(' '.join(sentences).split())
            custom_vectorizer = TfidfVectorizer(
                max_features=min(1000, max(20, total_words)),  # More generous max features
                stop_words=None,  # Don't filter stop words initially
                ngram_range=(1, 2),  # Include bigrams for better context
                min_df=1,  # Very lenient minimum document frequency
                max_df=0.95,  # Allow more common terms
                lowercase=True,
                token_pattern=r'\b[a-zA-Z][a-zA-Z0-9_-]*\b',  # More flexible token pattern
                sublinear_tf=True,  # Use sublinear TF scaling
                smooth_idf=True,  # Smooth IDF weights
                use_idf=True  # Use inverse document frequency
            )

            # Fit TF-IDF on sentences with error handling
            try:
                tfidf_matrix = custom_vectorizer.fit_transform(sentences)
                feature_names = custom_vectorizer.get_feature_names_out()

                if len(feature_names) == 0:
                    print("⚠️ TF-IDF produced empty vocabulary, using frequency method")
                    return self._extract_keywords_frequency(text, max_keywords)

            except ValueError as ve:
                if any(phrase in str(ve).lower() for phrase in ["empty vocabulary", "no terms remain", "after pruning"]):
                    print("⚠️ TF-IDF vocabulary empty, using frequency method")
                    return self._extract_keywords_frequency(text, max_keywords)
                else:
                    raise ve

            # Get average TF-IDF scores across all sentences
            if tfidf_matrix.shape[0] > 1:
                mean_scores = np.mean(tfidf_matrix.toarray(), axis=0)
            else:
                mean_scores = tfidf_matrix.toarray()[0]

            # Get top keywords by TF-IDF score with very low threshold
            top_indices = mean_scores.argsort()[-max_keywords*3:][::-1]  # Get more candidates
            keywords = []

            # Basic English stop words to filter out
            basic_stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use', 'this', 'that', 'with', 'have', 'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time', 'very', 'when', 'come', 'here', 'just', 'like', 'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them', 'well', 'were'}

            for i in top_indices:
                if i < len(feature_names) and mean_scores[i] > 0:  # Any positive score
                    keyword = feature_names[i].lower()
                    # Filter out stop words and very short words
                    if (len(keyword) > 2 and
                        not keyword.isdigit() and
                        keyword not in basic_stop_words and
                        keyword.isalpha()):
                        keywords.append(keyword)

            # If we still don't have enough keywords, be even more lenient
            if len(keywords) < max_keywords // 2:
                keywords = []
                for i in top_indices:
                    if i < len(feature_names) and mean_scores[i] > 0:
                        keyword = feature_names[i].lower()
                        if len(keyword) > 1 and not keyword.isdigit():
                            keywords.append(keyword)

            print(f"✅ TF-IDF analysis: processed {len(sentences)} documents, found {len(keywords)} keywords")
            return keywords[:max_keywords] if keywords else self._extract_keywords_frequency(text, max_keywords)

        except ValueError as ve:
            if any(phrase in str(ve).lower() for phrase in ["after pruning", "no terms remain", "empty vocabulary"]):
                print("⚠️ TF-IDF failed: No valid terms after filtering. Falling back to simple word frequency.")
                return self._extract_keywords_frequency(text, max_keywords)
            else:
                print(f"⚠️ TF-IDF extraction failed: {ve}")
                return self._extract_keywords_frequency(text, max_keywords)
        except Exception as e:
            print(f"⚠️ TF-IDF extraction failed: {e}")
            return self._extract_keywords_frequency(text, max_keywords)

    def _extract_keywords_frequency(self, text: str, max_keywords: int) -> List[str]:
        """Fallback keyword extraction using simple word frequency"""
        try:
            import re
            from collections import Counter

            # Clean text and extract words
            words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())

            # Remove common stop words
            stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'}
            words = [w for w in words if w not in stop_words and len(w) > 2]

            # Count word frequencies
            word_counts = Counter(words)

            # Get most common words
            keywords = [word for word, count in word_counts.most_common(max_keywords)]

            print(f"🔍 Frequency analysis: found {len(keywords)} keywords from {len(words)} words")
            return keywords

        except Exception as e:
            print(f"⚠️ Frequency analysis failed: {e}")
            return []

    def _extract_keywords_spacy(self, text: str, max_keywords: int) -> List[str]:
        """Extract keywords using spaCy"""
        doc = self.nlp(text)
        
        # Extract nouns, proper nouns, and adjectives
        keywords = []
        for token in doc:
            if (token.pos_ in ['NOUN', 'PROPN', 'ADJ'] and 
                not token.is_stop and 
                not token.is_punct and 
                len(token.text) > 2):
                keywords.append(token.lemma_.lower())
        
        # Count frequency and return most common
        keyword_counts = Counter(keywords)
        return [word for word, _ in keyword_counts.most_common(max_keywords)]
    
    def _extract_keywords_nltk(self, text: str, max_keywords: int) -> List[str]:
        """Extract keywords using NLTK"""
        try:
            tokens = word_tokenize(text)
            
            # Filter tokens
            keywords = []
            for token in tokens:
                if (len(token) > 2 and 
                    token.lower() not in self.stop_words and 
                    token.isalpha()):
                    if self.stemmer:
                        keywords.append(self.stemmer.stem(token.lower()))
                    else:
                        keywords.append(token.lower())
            
            # Count frequency and return most common
            keyword_counts = Counter(keywords)
            return [word for word, _ in keyword_counts.most_common(max_keywords)]
        except:
            return self._extract_keywords_simple(text, max_keywords)
    
    def _extract_keywords_simple(self, text: str, max_keywords: int) -> List[str]:
        """Simple keyword extraction without external libraries"""
        # Basic stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they'
        }
        
        words = text.split()
        keywords = []
        
        for word in words:
            if (len(word) > 2 and 
                word.lower() not in stop_words and 
                word.isalpha()):
                keywords.append(word.lower())
        
        # Count frequency and return most common
        keyword_counts = Counter(keywords)
        return [word for word, _ in keyword_counts.most_common(max_keywords)]
    
    def suggest_tags(self, text: str, num_tags: int = 5) -> List[str]:
        """Suggest tags based on text content"""
        if not isinstance(text, str):
            if text is None:
                return []
            text = str(text)

        keywords = self.extract_keywords(text, num_tags * 2)
        
        # Filter and clean keywords for tags
        tags = []
        for keyword in keywords:
            if len(keyword) > 2 and keyword.isalpha():
                tags.append(keyword.replace('_', ' ').title())
        
        return tags[:num_tags]
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Basic sentiment analysis"""
        if not isinstance(text, str):
            if text is None:
                return {'sentiment': 'neutral', 'confidence': 0.0}
            text = str(text)

        if not text:
            return {'sentiment': 'neutral', 'confidence': 0.0}
        
        # Simple sentiment analysis using word lists
        positive_words = {
            'good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 
            'positive', 'beneficial', 'useful', 'valuable', 'important', 'significant'
        }
        
        negative_words = {
            'bad', 'terrible', 'awful', 'horrible', 'negative', 'poor', 'useless', 
            'problematic', 'difficult', 'challenging', 'limited', 'incomplete'
        }
        
        words = self._clean_text(text).split()
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)
        
        total_sentiment_words = positive_count + negative_count
        
        if total_sentiment_words == 0:
            return {'sentiment': 'neutral', 'confidence': 0.0}
        
        if positive_count > negative_count:
            sentiment = 'positive'
            confidence = positive_count / total_sentiment_words
        elif negative_count > positive_count:
            sentiment = 'negative'
            confidence = negative_count / total_sentiment_words
        else:
            sentiment = 'neutral'
            confidence = 0.5
        
        return {
            'sentiment': sentiment,
            'confidence': round(confidence, 2),
            'positive_words': positive_count,
            'negative_words': negative_count
        }
    
    def extract_entities(self, text: str) -> List[Dict[str, str]]:
        """Extract named entities using advanced NER (BERT-based + spaCy)"""
        if not isinstance(text, str):
            if text is None:
                return []
            text = str(text)

        if not text:
            return []

        print(f"🔍 Extracting entities using advanced NER techniques...")
        entities = []

        # Try BERT-based NER first
        if self.ner_pipeline:
            try:
                bert_entities = self.ner_pipeline(text)
                for ent in bert_entities:
                    entities.append({
                        'text': ent['word'],
                        'label': ent['entity_group'],
                        'confidence': round(ent['score'], 3),
                        'description': self._get_entity_description(ent['entity_group']),
                        'method': 'BERT-NER'
                    })
                print(f"✅ BERT-NER extracted {len(bert_entities)} entities")
            except Exception as e:
                print(f"⚠️ BERT-NER failed: {e}")

        # Also use spaCy NER for comparison
        if self.nlp:
            try:
                doc = self.nlp(text)
                spacy_entities = []
                for ent in doc.ents:
                    spacy_entities.append({
                        'text': ent.text,
                        'label': ent.label_,
                        'confidence': 1.0,  # spaCy doesn't provide confidence scores
                        'description': spacy.explain(ent.label_) if spacy.explain(ent.label_) else ent.label_,
                        'method': 'spaCy-NER'
                    })
                entities.extend(spacy_entities)
                print(f"✅ spaCy-NER extracted {len(spacy_entities)} entities")
            except Exception as e:
                print(f"⚠️ spaCy-NER failed: {e}")

        # Remove duplicates and sort by confidence
        unique_entities = self._deduplicate_entities(entities)
        print(f"🎯 Total unique entities extracted: {len(unique_entities)}")

        return unique_entities

    def _get_entity_description(self, entity_label: str) -> str:
        """Get description for entity labels"""
        descriptions = {
            'PER': 'Person',
            'PERSON': 'Person',
            'ORG': 'Organization',
            'ORGANIZATION': 'Organization',
            'LOC': 'Location',
            'LOCATION': 'Location',
            'MISC': 'Miscellaneous',
            'GPE': 'Geopolitical Entity',
            'DATE': 'Date',
            'TIME': 'Time',
            'MONEY': 'Money',
            'PERCENT': 'Percentage',
            'FACILITY': 'Facility',
            'PRODUCT': 'Product'
        }
        return descriptions.get(entity_label.upper(), entity_label)

    def _deduplicate_entities(self, entities: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Remove duplicate entities and keep highest confidence ones"""
        entity_map = {}

        for entity in entities:
            text = entity['text'].lower().strip()
            if text not in entity_map or entity['confidence'] > entity_map[text]['confidence']:
                entity_map[text] = entity

        # Sort by confidence descending
        return sorted(entity_map.values(), key=lambda x: x['confidence'], reverse=True)
    
    def generate_summary(self, text: str, max_sentences: int = 3) -> str:
        """Generate a simple extractive summary"""
        if not isinstance(text, str):
            if text is None:
                return ""
            text = str(text)

        if not text:
            return ""
        
        # Split into sentences
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        
        if len(sentences) <= max_sentences:
            return '. '.join(sentences) + '.'
        
        # Score sentences based on keyword frequency
        keywords = self.extract_keywords(text, 10)
        sentence_scores = {}
        
        for i, sentence in enumerate(sentences):
            score = 0
            sentence_words = self._clean_text(sentence).split()
            for word in sentence_words:
                if word in keywords:
                    score += 1
            sentence_scores[i] = score
        
        # Select top sentences
        top_sentences = sorted(sentence_scores.items(), key=lambda x: x[1], reverse=True)[:max_sentences]
        top_sentences.sort(key=lambda x: x[0])  # Sort by original order
        
        summary_sentences = [sentences[i] for i, _ in top_sentences]
        return '. '.join(summary_sentences) + '.'

    def get_bert_embeddings(self, text: str) -> Optional[np.ndarray]:
        """Get BERT embeddings for text"""
        if not self.bert_model or not self.bert_tokenizer:
            return None

        try:
            # Tokenize and encode
            inputs = self.bert_tokenizer(text, return_tensors="pt", truncation=True, max_length=512)

            # Get embeddings
            with torch.no_grad():
                outputs = self.bert_model(**inputs)
                # Use mean pooling of last hidden states
                embeddings = outputs.last_hidden_state.mean(dim=1).squeeze().numpy()

            print(f"✅ BERT embeddings generated: {embeddings.shape}")
            return embeddings

        except Exception as e:
            print(f"⚠️ BERT embedding generation failed: {e}")
            return None

    def semantic_similarity(self, text1: str, text2: str) -> float:
        """Calculate semantic similarity using BERT embeddings"""
        if not self.bert_model:
            return 0.0

        try:
            emb1 = self.get_bert_embeddings(text1)
            emb2 = self.get_bert_embeddings(text2)

            if emb1 is None or emb2 is None:
                return 0.0

            # Calculate cosine similarity
            similarity = cosine_similarity([emb1], [emb2])[0][0]
            print(f"🔍 Semantic similarity calculated: {similarity:.3f}")
            return float(similarity)

        except Exception as e:
            print(f"⚠️ Semantic similarity calculation failed: {e}")
            return 0.0

    def advanced_content_analysis(self, text: str) -> Dict[str, Any]:
        """Perform comprehensive content analysis using all available NLP techniques"""
        if not isinstance(text, str):
            if text is None:
                return {}
            text = str(text)

        print(f"🚀 Starting advanced content analysis...")

        analysis = {
            'text_length': len(text),
            'word_count': len(text.split()),
            'sentence_count': len(sent_tokenize(text)) if NLTK_AVAILABLE else len(text.split('.')),
            'processing_methods': []
        }

        # Extract keywords with multiple methods
        analysis['keywords'] = self.extract_keywords(text, 15)
        analysis['processing_methods'].append('TF-IDF/spaCy/NLTK keyword extraction')

        # Extract entities with advanced NER
        analysis['entities'] = self.extract_entities(text)
        analysis['processing_methods'].append('BERT-NER + spaCy entity recognition')

        # Sentiment analysis
        analysis['sentiment'] = self.analyze_sentiment(text)
        analysis['processing_methods'].append('Lexicon-based sentiment analysis')

        # Generate summary
        analysis['summary'] = self.generate_summary(text, 3)
        analysis['processing_methods'].append('Extractive summarization')

        # BERT embeddings if available
        if self.bert_model:
            embeddings = self.get_bert_embeddings(text)
            if embeddings is not None:
                analysis['bert_embedding_dim'] = embeddings.shape[0]
                analysis['processing_methods'].append('BERT embeddings')

        # TF-IDF analysis if available
        if self.tfidf_vectorizer:
            analysis['processing_methods'].append('TF-IDF vectorization')

        print(f"✅ Advanced content analysis complete: {len(analysis['processing_methods'])} methods used")
        return analysis

    def generate_enhanced_description(self, dataset_info: Dict[str, Any]) -> str:
        """Generate enhanced dataset description using Free AI models with FLAN-T5 fallback"""
        print("🔍 Generating enhanced description using Free AI models...")
        print(f"🔍 Free AI available: {FREE_AI_AVAILABLE}")
        print(f"🔍 FLAN-T5 Model available: {self.t5_model is not None}")
        print(f"🔍 Dataset info keys: {list(dataset_info.keys())}")

        # Try Free AI models first (Mistral, Groq)
        if FREE_AI_AVAILABLE:
            try:
                print("🔄 Attempting Free AI description generation...")
                description = free_ai_service.generate_enhanced_description(dataset_info)
                if description and len(description.strip()) > 100:
                    # Clean the description to remove any remaining special characters
                    cleaned_description = self._clean_ai_description(description)
                    print(f"✅ Free AI description generated successfully: {len(cleaned_description)} chars")
                    return cleaned_description
                else:
                    print(f"⚠️ Free AI returned short description: '{description}'")
            except Exception as e:
                print(f"⚠️ Free AI models failed: {e}")

        # Fallback to FLAN-T5 Base model (offline capability)
        if self.t5_model is not None and self.t5_tokenizer is not None:
            try:
                print("🔄 Attempting FLAN-T5 Base description generation...")
                description = self._generate_description_t5(dataset_info)
                if description and len(description.strip()) > 50:
                    # Clean the description to remove any special characters
                    cleaned_description = self._clean_ai_description(description)
                    print(f"✅ FLAN-T5 Base description generated successfully: {len(cleaned_description)} chars")
                    return cleaned_description
                else:
                    print(f"⚠️ FLAN-T5 generated short description: '{description}'")
            except Exception as e:
                print(f"⚠️ FLAN-T5 model failed: {e}")
                import traceback
                traceback.print_exc()

        print("⚠️ All AI models unavailable or failed, using advanced local NLP description generation")
        return self._generate_comprehensive_local_description(dataset_info)

    def _clean_ai_description(self, description: str) -> str:
        """
        Clean AI-generated description by removing unnecessary characters and formatting

        Args:
            description: Raw AI-generated description

        Returns:
            Cleaned description without special characters
        """
        if not description or not isinstance(description, str):
            return ""

        # Remove markdown formatting
        description = re.sub(r'\*\*([^*]+)\*\*', r'\1', description)  # Bold
        description = re.sub(r'\*([^*]+)\*', r'\1', description)      # Italic
        description = re.sub(r'`([^`]+)`', r'\1', description)        # Code
        description = re.sub(r'#{1,6}\s*([^\n]+)', r'\1', description)  # Headers

        # Remove special characters but keep basic punctuation
        description = re.sub(r'[#@$%^&*+=\[\]{}|\\<>~`]', '', description)

        # Clean up multiple spaces and newlines
        description = re.sub(r'\n+', ' ', description)
        description = re.sub(r'\s+', ' ', description)

        # Remove leading/trailing whitespace
        description = description.strip()

        # Ensure proper sentence structure
        if description and not description.endswith('.'):
            description += '.'

        return description

    def _generate_description_t5(self, dataset_info: Dict[str, Any]) -> str:
        """Generate comprehensive description using T5 model with advanced prompting"""
        try:
            print(f"🔄 T5 generation starting with dataset_info: {dataset_info}")

            # Extract comprehensive dataset information with better defaults
            title = dataset_info.get('title', 'Unknown Dataset')
            field_names = dataset_info.get('field_names', [])
            record_count = dataset_info.get('record_count', 0)
            data_types = dataset_info.get('data_types', [])
            keywords = dataset_info.get('keywords', [])
            entities = dataset_info.get('entities', [])
            sample_data = dataset_info.get('sample_data', [])
            format_type = dataset_info.get('format', dataset_info.get('data_type', 'data'))
            use_cases = dataset_info.get('use_cases', [])
            category = dataset_info.get('category', 'General')
            summary = dataset_info.get('summary', '')

            print(f"🔍 Extracted info - Title: {title}, Records: {record_count}, Fields: {len(field_names)}")
            print(f"🔍 Keywords: {keywords[:3]}, Format: {format_type}, Category: {category}")

            # Create dynamic, dataset-specific prompts
            descriptions = []

            # 1. Generate dataset-specific overview
            overview_prompt = self._create_dynamic_overview_prompt(title, record_count, field_names, format_type, keywords, category)
            print(f"🔄 Overview prompt: {overview_prompt[:100]}...")
            overview_desc = self._generate_t5_text(overview_prompt, max_length=180, min_length=50)
            if overview_desc and len(overview_desc.strip()) > 20:
                descriptions.append(overview_desc)
                print(f"✅ Overview generated: {len(overview_desc)} chars")

            # 2. Generate field-specific structure description
            if field_names and len(field_names) > 0:
                structure_prompt = self._create_dynamic_structure_prompt(field_names, data_types, sample_data, title)
                print(f"🔄 Structure prompt: {structure_prompt[:100]}...")
                structure_desc = self._generate_t5_text(structure_prompt, max_length=150, min_length=40)
                if structure_desc and len(structure_desc.strip()) > 20:
                    descriptions.append(structure_desc)
                    print(f"✅ Structure generated: {len(structure_desc)} chars")

            # 3. Generate content-specific use case description
            if keywords or entities or summary:
                usecase_prompt = self._create_dynamic_usecase_prompt(title, keywords, entities, summary, category)
                print(f"🔄 Use case prompt: {usecase_prompt[:100]}...")
                usecase_desc = self._generate_t5_text(usecase_prompt, max_length=120, min_length=30)
                if usecase_desc and len(usecase_desc.strip()) > 20:
                    descriptions.append(usecase_desc)
                    print(f"✅ Use case generated: {len(usecase_desc)} chars")

            # Combine all descriptions into a comprehensive description
            if descriptions:
                combined_description = self._combine_dynamic_descriptions(descriptions, dataset_info)
                print(f"✅ Comprehensive T5 description generated: {len(combined_description)} chars")
                return combined_description
            else:
                # Fallback to enhanced simple generation
                print("⚠️ No T5 descriptions generated, using enhanced fallback")
                return self._generate_enhanced_simple_description(dataset_info)

        except Exception as e:
            print(f"⚠️ T5 description generation failed: {e}")
            import traceback
            traceback.print_exc()
            return ""

    def _create_overview_prompt(self, title: str, record_count: int, field_count: int, format_type: str, keywords: List[str]) -> str:
        """Create overview prompt for T5"""
        keyword_text = f" with topics including {', '.join(keywords[:5])}" if keywords else ""
        return f"describe dataset: {title} contains {record_count:,} records in {field_count} fields, {format_type} format{keyword_text}. comprehensive research dataset for analysis."

    def _create_structure_prompt(self, field_names: List[str], data_types: List[str], sample_data: List[Dict]) -> str:
        """Create structure prompt for T5"""
        fields_text = ', '.join(field_names[:8])
        types_text = ', '.join(list(set(data_types))[:5]) if data_types else "mixed types"
        return f"explain data structure: fields {fields_text} with data types {types_text}. organized tabular data for statistical analysis."

    def _create_usecase_prompt(self, title: str, keywords: List[str], entities: List[Dict], use_cases: List[str]) -> str:
        """Create use case prompt for T5"""
        keyword_text = ', '.join(keywords[:5]) if keywords else "general analysis"
        entity_text = ', '.join([e.get('text', '') for e in entities[:3]]) if entities else ""
        context = f"{keyword_text} {entity_text}".strip()
        return f"applications for {title}: {context}. suitable for research, machine learning, statistical modeling, data science projects."

    def _generate_t5_text(self, prompt: str, max_length: int = 150, min_length: int = 30) -> str:
        """Generate text using T5 with specific parameters"""
        try:
            if not self.t5_model or not self.t5_tokenizer:
                return ""

            # Prepare input
            t5_input = f"summarize: {prompt}"
            inputs = self.t5_tokenizer.encode(t5_input, return_tensors="pt", max_length=512, truncation=True)

            # Generate
            with torch.no_grad():
                outputs = self.t5_model.generate(
                    inputs,
                    max_length=max_length,
                    min_length=min_length,
                    length_penalty=1.5,
                    num_beams=3,
                    early_stopping=True,
                    do_sample=False,
                    repetition_penalty=1.2
                )

            return self.t5_tokenizer.decode(outputs[0], skip_special_tokens=True)
        except Exception as e:
            print(f"⚠️ T5 text generation failed: {e}")
            return ""

    def _combine_t5_descriptions(self, descriptions: List[str], dataset_info: Dict[str, Any]) -> str:
        """Combine multiple T5 descriptions into a comprehensive description"""
        try:
            title = dataset_info.get('title', 'Dataset')
            record_count = dataset_info.get('record_count', 0)
            field_names = dataset_info.get('field_names', [])

            # Start with overview
            combined_parts = []

            if descriptions:
                # Add main description
                combined_parts.append(descriptions[0])

                # Add structural information
                if len(descriptions) > 1:
                    combined_parts.append(descriptions[1])

                # Add use case information
                if len(descriptions) > 2:
                    combined_parts.append(descriptions[2])

            # Add specific details
            if record_count and len(field_names):
                combined_parts.append(f"The dataset comprises {record_count:,} records organized across {len(field_names)} distinct fields, providing a robust foundation for comprehensive data analysis and research applications.")

            # Add field details for smaller datasets
            if field_names and len(field_names) <= 10:
                combined_parts.append(f"Key data fields include: {', '.join(field_names)}.")
            elif field_names:
                combined_parts.append(f"Primary data fields include: {', '.join(field_names[:6])}, among {len(field_names)} total variables.")

            # Add methodological suggestions
            combined_parts.append("This dataset supports various analytical approaches including statistical modeling, machine learning applications, and exploratory data analysis.")

            return " ".join(combined_parts)

        except Exception as e:
            print(f"⚠️ T5 description combination failed: {e}")
            return descriptions[0] if descriptions else ""

    def _generate_simple_t5_description(self, dataset_info: Dict[str, Any]) -> str:
        """Generate simple T5 description as fallback"""
        try:
            title = dataset_info.get('title', 'Dataset')
            record_count = dataset_info.get('record_count', 0)
            field_count = len(dataset_info.get('field_names', []))

            simple_prompt = f"describe: {title} dataset with {record_count:,} records and {field_count} fields for research analysis"
            return self._generate_t5_text(simple_prompt, max_length=200, min_length=50)
        except Exception as e:
            print(f"⚠️ Simple T5 description failed: {e}")
            return ""

    def _enhance_t5_description(self, base_description: str, dataset_info: Dict[str, Any]) -> str:
        """Enhance T5 generated description with additional context"""
        try:
            title = dataset_info.get('title', 'Dataset')
            record_count = dataset_info.get('record_count', 0)
            field_names = dataset_info.get('field_names', [])

            # Start with T5 description
            enhanced_parts = [base_description.strip()]

            # Add specific details
            if record_count and len(field_names):
                enhanced_parts.append(f"The dataset contains {record_count:,} records organized across {len(field_names)} fields, providing a comprehensive data structure for analysis.")

            # Add field information
            if field_names and len(field_names) <= 8:
                enhanced_parts.append(f"Key fields include: {', '.join(field_names)}.")
            elif field_names:
                enhanced_parts.append(f"Primary fields include: {', '.join(field_names[:5])}, among {len(field_names)} total variables.")

            # Add use case suggestions
            enhanced_parts.append("This dataset is well-suited for statistical analysis, data mining, and research applications.")

            return " ".join(enhanced_parts)

        except Exception as e:
            print(f"⚠️ T5 description enhancement failed: {e}")
            return base_description

# Removed free AI model methods - focusing on offline T5 model

    def _generate_comprehensive_local_description(self, dataset_info: Dict[str, Any]) -> str:
        """Generate comprehensive description using advanced local NLP techniques"""
        try:
            title = dataset_info.get('title', 'Dataset')
            field_names = dataset_info.get('field_names', [])
            record_count = dataset_info.get('record_count', 0)
            data_types = dataset_info.get('data_types', [])
            keywords = dataset_info.get('keywords', [])
            category = dataset_info.get('category', '')

            description_parts = []

            # Enhanced introduction with category context
            if category:
                description_parts.append(f"This {category.lower()} dataset, titled '{title}', represents a comprehensive collection of structured data designed for advanced analytical and research applications.")
            else:
                description_parts.append(f"This dataset, titled '{title}', represents a comprehensive collection of structured data designed for advanced analytical and research applications.")

            # Detailed data structure analysis
            if record_count and field_names:
                description_parts.append(f"The dataset encompasses {record_count:,} records systematically organized across {len(field_names)} distinct fields, providing a robust foundation for statistical analysis and data mining operations.")
            elif record_count:
                description_parts.append(f"The dataset contains {record_count:,} records, offering substantial data volume for comprehensive analysis.")

            # Advanced field analysis
            if field_names:
                if len(field_names) <= 8:
                    field_list = ", ".join(field_names)
                    description_parts.append(f"The dataset structure includes the following fields: {field_list}.")
                else:
                    primary_fields = field_names[:6]
                    field_list = ", ".join(primary_fields)
                    description_parts.append(f"Primary fields include: {field_list}, along with {len(field_names) - 6} additional variables providing comprehensive data coverage.")

            # Enhanced data type analysis
            if data_types:
                unique_types = list(set(data_types))
                if 'object' in unique_types or 'string' in unique_types:
                    description_parts.append("The dataset incorporates textual data elements enabling qualitative analysis and natural language processing applications.")

                if any(dtype in ['int64', 'float64', 'numeric'] for dtype in unique_types):
                    description_parts.append("Numerical data components support statistical modeling, mathematical analysis, and quantitative research methodologies.")

            # Advanced keyword and topic analysis
            if keywords:
                if len(keywords) <= 8:
                    keyword_list = ", ".join(keywords)
                    description_parts.append(f"The dataset encompasses key concepts and topics including: {keyword_list}, indicating its relevance for domain-specific research and analysis.")
                else:
                    primary_keywords = keywords[:6]
                    keyword_list = ", ".join(primary_keywords)
                    description_parts.append(f"Primary thematic elements include: {keyword_list}, among {len(keywords) - 6} additional conceptual dimensions.")

            # Comprehensive use case analysis
            use_cases = []

            if record_count and record_count > 1000:
                use_cases.append("large-scale statistical analysis and machine learning model development")
            elif record_count and record_count > 100:
                use_cases.append("statistical analysis and predictive modeling")

            if len(field_names) > 10:
                use_cases.append("multivariate analysis and feature engineering")

            # Default use cases
            if not use_cases:
                use_cases = ["exploratory data analysis", "statistical modeling", "data visualization", "research applications"]

            description_parts.append(f"This dataset is particularly well-suited for {', '.join(use_cases[:-1])}, and {use_cases[-1]}.")

            # Quality and methodology note
            description_parts.append("The structured nature of this dataset, combined with its comprehensive field coverage, makes it a valuable resource for researchers, analysts, and data scientists seeking to derive meaningful insights through rigorous analytical methodologies.")

            return " ".join(description_parts)

        except Exception as e:
            print(f"⚠️ Comprehensive local description generation failed: {e}")
            return self._generate_basic_description(dataset_info)

    def _generate_basic_description(self, dataset_info: Dict[str, Any]) -> str:
        """Generate basic description as fallback"""
        title = dataset_info.get('title', 'Dataset')
        record_count = dataset_info.get('record_count', 0)
        field_count = len(dataset_info.get('field_names', []))
        keywords = dataset_info.get('keywords', [])

        description_parts = [
            f"This dataset, titled '{title}', contains {record_count:,} records with {field_count} fields."
        ]

        if keywords:
            description_parts.append(f"Key topics include: {', '.join(keywords[:5])}.")

        if record_count > 1000:
            description_parts.append("This is a substantial dataset suitable for comprehensive analysis.")
        elif record_count > 100:
            description_parts.append("This dataset provides a good sample size for analysis.")

        description_parts.append("The dataset can be used for research, analysis, and data science applications.")

        return ' '.join(description_parts)

    def analyze_content_basic(self, text_content: str, field_names: List[str] = None) -> Dict[str, Any]:
        """Basic content analysis using existing methods"""
        if not isinstance(text_content, str):
            text_content = str(text_content) if text_content else ""

        return {
            'keywords': self.extract_keywords(text_content, 15),
            'entities': self.extract_entities(text_content),
            'summary': self.generate_summary(text_content),
            'sentiment': self.analyze_sentiment(text_content),
            'tags': self.suggest_tags(text_content, 8)
        }


# Global service instance
nlp_service = NLPService()


def get_nlp_service() -> NLPService:
    """Get the NLP service instance"""
    return nlp_service
