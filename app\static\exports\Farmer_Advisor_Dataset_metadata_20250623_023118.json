{"export_info": {"export_format": "JSON", "export_date": "2025-06-23T02:31:18.384552", "generated_by": "AI-Powered Metadata Harvesting System", "version": "2.0"}, "basic_info": {"id": "68447daceb40764b7c35fb93", "title": "Farmer Advisor Dataset", "source": "File Upload", "category": "General", "data_type": "", "format": "csv", "file_size": null, "license": null, "created_at": "2025-06-07T17:58:04.436000", "updated_at": "2025-06-07T18:02:49.408000", "source_url": "", "persistent_identifier": null, "encoding_format": null, "status": "completed"}, "description": {"main_description": "This comprehensive dataset, titled \"Farmer Advisor Dataset\", represents a valuable collection of general data containing 10,000 records across 3 distinct fields.\n\nThe dataset provides extensive coverage of key attributes and variables, making it an excellent resource for research, analysis, and data science applications. With its structured format and comprehensive scope, this dataset enables researchers and analysts to explore patterns, trends, and relationships within the general space.\n\nKey characteristics include:\n- Comprehensive data coverage with 10,000 individual records\n- Well-structured format with 3 distinct data fields\n- Domain-specific focus on general\n- Suitable for both exploratory and confirmatory data analysis\n\nThis dataset is particularly valuable for agricultural research and planning, offering researchers the opportunity to conduct meaningful analysis and derive actionable insights from the data.", "structured_description": {"overview": {"text": "This dataset contains 10,000 records with 3 fields, focusing on general.", "type": "auto_generated", "enhanced": true}, "data_structure": {"description": "The dataset is organized with 3 columns and 10,000 rows of observations.", "field_names": ["test_field1", " test_field2", " test_field3"], "total_fields": 3, "total_records": 10000}, "use_cases": {"applications": ["Agricultural research and planning", " Crop yield prediction", " Farming optimization strategies", " Agricultural policy analysis", " Climate impact studies", " Sustainable farming research"]}, "quality_aspects": {"completeness": "Comprehensive field coverage", "consistency": "Structured format ensures consistency", "reliability": "Sourced from File Upload"}, "metadata": {"auto_generated": true, "version": "2.0", "enhanced": true}}, "auto_generated_description": null}, "data_statistics": {"record_count": 10000, "field_count": 3, "field_names": ["test_field1", " test_field2", " test_field3"], "data_types": ["object", " int64", " float64"], "data_distribution_types": null}, "metadata_fields": {"tags": [], "keywords": ["[\"soybean\"", " \"dataset\"", " \"wheat\"", " \"farmer\"", " \"advisor\"", " \"corn\"", " \"this\"", " \"contains\"", " \"records\"", " \"with\"", " \"fields\"", " \"including\"", " \"key\"", " \"topics\"", " \"include\"", " \"Farm ID\"", " \"Soil pH\"", " \"Soil Moisture\"", " \"Temperature C\"", " \"Rainfall mm\"]"], "use_cases": ["Agricultural research and planning", " Crop yield prediction", " Farming optimization strategies", " Agricultural policy analysis", " Climate impact studies", " Sustainable farming research"], "entities": null, "sentiment": null}, "quality_assessment": {"overall_quality_score": 68.04155844155845, "completeness": 80.9090909090909, "consistency": 36.0, "accuracy": 79.0, "timeliness": 75.5, "conformity": 60.42857142857143, "integrity": 81.0, "issues": ["Missing required field: description", "Missing required field: source", "Missing required field: description", "Missing required field: source", "Inconsistent field definition for Farm_ID", "Inconsistent field definition for Soil_pH", "Inconsistent field definition for Soil_Moisture", "Inconsistent field definition for Temperature_C", "Inconsistent field definition for Rainfall_mm", "Inconsistent field definition for Crop_Type", "Inconsistent field definition for Fertilizer_Usage_kg", "Inconsistent field definition for Pesticide_Usage_kg", "Inconsistent field definition for Crop_Yield_ton", "Inconsistent field definition for Sustainability_Score", "Inconsistent field definition for Student_ID", "Inconsistent field definition for Age", "Inconsistent field definition for Gender", "Inconsistent field definition for Education_Level", "Inconsistent field definition for Course_Name", "Inconsistent field definition for Time_Spent_on_Videos", "Inconsistent field definition for Quiz_Attempts", "Inconsistent field definition for Quiz_Scores", "Inconsistent field definition for Forum_Participation", "Inconsistent field definition for Assignment_Completion_Rate", "Inconsistent field definition for Engagement_Level", "Inconsistent field definition for Final_Exam_Score", "Inconsistent field definition for Learning_Style", "Inconsistent field definition for Feedback_Score", "Inconsistent field definition for Dropout_Likelihood", "Inconsistent field definition for Farm_ID", "Inconsistent field definition for Soil_pH", "Inconsistent field definition for Soil_Moisture", "Inconsistent field definition for Temperature_C", "Inconsistent field definition for Rainfall_mm", "Inconsistent field definition for Crop_Type", "Inconsistent field definition for Fertilizer_Usage_kg", "Inconsistent field definition for Pesticide_Usage_kg", "Inconsistent field definition for Crop_Yield_ton", "Inconsistent field definition for Sustainability_Score", "Schema.org metadata not defined", "Schema.org metadata not defined"], "recommendations": ["Add description to improve dataset completeness", "Add source to improve dataset completeness", "Consider adding these fields to enhance completeness: author, category, keywords", "Add description to improve dataset completeness", "Add source to improve dataset completeness", "Consider adding these fields to enhance completeness: author, category, keywords", "Consider adding these fields to enhance completeness: author, license, schema", "Specify update frequency to improve timeliness assessment", "Specify update frequency to improve timeliness assessment", "Specify update frequency to improve timeliness assessment", "Define Schema.org metadata to improve standards conformity", "Assign a persistent identifier to improve findability", "Ensure the dataset is indexed in searchable resources", "Provide a standard access URL to improve accessibility", "Use FAIR vocabularies to improve interoperability", "Include qualified references to related datasets", "Specify a clear license to improve reusability", "Add detailed provenance information", "Define Schema.org metadata to improve standards conformity", "Assign a persistent identifier to improve findability", "Ensure the dataset is indexed in searchable resources", "Provide a standard access URL to improve accessibility", "Use FAIR vocabularies to improve interoperability", "Include qualified references to related datasets", "Specify a clear license to improve reusability", "Add detailed provenance information", "Assign a persistent identifier to improve findability", "Ensure the dataset is indexed in searchable resources", "Provide a standard access URL to improve accessibility", "Use FAIR vocabularies to improve interoperability", "Include qualified references to related datasets", "Specify a clear license to improve reusability", "Add detailed provenance information"], "assessment_date": "2025-06-07T17:58:37.464000"}, "fair_compliance": {"overall_score": 84.0, "is_compliant": true, "findable_score": 72.0, "accessible_score": 100.0, "interoperable_score": 84.0, "reusable_score": 80.0}, "standards_compliance": {"schema_org_compliant": false, "dublin_core": {}, "dcat_metadata": {}, "json_ld": {}}, "visualizations": {"quality_metrics": {"type": "quality_metrics", "data": {"overall_score": 68.04155844155845, "completeness": 80.9090909090909, "consistency": 36.0, "accuracy": 79.0, "fair_compliant": false}, "chart_config": {"type": "radar", "title": "Data Quality Assessment", "description": "Comprehensive quality metrics for the dataset"}}, "data_overview": {"type": "data_overview", "data": {"record_count": 10000, "field_count": 10, "data_size": "10000 rows × 10 columns", "estimated_size_mb": 0.76}, "chart_config": {"type": "info_cards", "title": "Dataset Overview", "description": "Basic statistics about the dataset structure"}}, "field_analysis": {"type": "field_analysis", "data": {"type_distribution": {"int64": 1, "float64": 8, "object": 1}, "numeric_fields": 0, "text_fields": 0, "categorical_fields": 0, "datetime_fields": 0}, "chart_config": {"type": "pie", "title": "Field Type Distribution", "description": "Distribution of data types across dataset fields"}}, "keyword_cloud": {"type": "keyword_cloud", "data": {"keywords": [{"text": "soybean", "weight": 21}, {"text": "dataset", "weight": 20}, {"text": "wheat", "weight": 19}, {"text": "farmer", "weight": 18}, {"text": "advisor", "weight": 17}, {"text": "corn", "weight": 16}, {"text": "this", "weight": 15}, {"text": "contains", "weight": 14}, {"text": "records", "weight": 13}, {"text": "with", "weight": 12}, {"text": "fields", "weight": 11}, {"text": "including", "weight": 10}, {"text": "key", "weight": 9}, {"text": "topics", "weight": 8}, {"text": "include", "weight": 7}], "total_keywords": 15}, "chart_config": {"type": "wordcloud", "title": "Content Keywords", "description": "Most relevant keywords extracted from dataset content"}}, "metadata_completeness": {"type": "metadata_completeness", "data": {"completeness_fields": {"title": true, "description": true, "source": true, "category": true, "tags": false, "quality_assessed": true, "schema_defined": false}, "completed_count": 5, "total_count": 7, "completeness_percentage": 71.4}, "chart_config": {"type": "progress_bar", "title": "Metadata Completeness", "description": "71.4% of metadata fields are complete"}}, "data_distribution": {"type": "histogram", "data": {"bins": ["1.0-30.6", "30.6-60.1", "60.1-89.7", "89.7-119.2", "119.2-148.8", "148.8-178.4", "178.4-207.9", "207.9-237.5", "237.5-267.1", "267.1-296.6"], "frequencies": [57, 12, 4, 3, 3, 2, 5, 1, 1, 2], "xlabel": "Value Range", "total_values": 90}, "chart_config": {"type": "histogram", "title": "Data Distribution", "description": "Distribution of 90 numeric values"}}, "correlation_analysis": {"type": "heatmap", "data": {"matrix": [[1.0, -0.17588524320831478, -0.4132049219455559, -0.2595950356026839, -0.0015800863612527947], [-0.17588524320831478, 1.0, 0.8886469970902554, 0.31484516914923916, -0.28963567190597084], [-0.4132049219455559, 0.8886469970902555, 1.0, 0.2581189903654828, -0.24031800272610518], [-0.2595950356026839, 0.31484516914923916, 0.2581189903654828, 1.0, 0.3443996343457201], [-0.0015800863612527945, -0.28963567190597084, -0.24031800272610518, 0.3443996343457201, 1.0]], "labels": {"x": ["Farm_ID", "Soil_pH", "Soil_Moisture", "Temperature_C", "Rainfall_mm"], "y": ["Farm_ID", "Soil_pH", "Soil_Moisture", "Temperature_C", "Rainfall_mm"]}, "max": 1.0}, "chart_config": {"type": "heatmap", "title": "Correlation Analysis", "description": "Correlation matrix for 5 numeric fields"}}, "trend_analysis": {"type": "line", "data": {"labels": ["Record 0", "Record 10", "Record 20", "Record 30", "Record 40", "Record 50", "Record 60", "Record 70", "Record 80", "Record 90"], "values": [87.0, 83.1, 87.2, 83.3, 87.4, 83.5, 87.6, 83.7, 87.8, 83.9], "x": [0, 10, 20, 30, 40, 50, 60, 70, 80, 90], "y": [87.0, 83.1, 87.2, 83.3, 87.4, 83.5, 87.6, 83.7, 87.8, 83.9]}, "chart_config": {"type": "line", "title": "Data Quality Trend", "description": "Quality trend across 10 sample points"}}, "generated_at": "2025-06-07T19:02:49.372881", "visualization_version": "3.0"}, "health_report": null, "ai_compliance": null, "processing_metadata": null, "python_examples": {"basic_loading": "# Load Farmer Advisor Dataset dataset\nimport pandas as pd\nimport numpy as np\n\n# Load the dataset\ndf = pd.read_csv('farmer_advisor_dataset.csv')\n\n# Basic information\nprint(f\"Dataset shape: {df.shape}\")\nprint(f\"Columns: {list(df.columns)}\")\nprint(df.head())", "data_exploration": "# Data exploration for Farmer Advisor Dataset\nimport matplotlib.pyplot as plt\nimport seaborn as sns\n\n# Dataset overview\nprint(df.info())\nprint(df.describe())\n\n# Check for missing values\nprint(df.isnull().sum())\n\n# Basic visualizations\nplt.figure(figsize=(10, 6))\n# Analyze test_field2\n# Analyze test_field3"}, "nlp_analysis": {"entities": null, "sentiment": null, "summary": null, "language_detected": "en", "content_analysis": null}, "technical_metadata": {"file_format": "csv", "encoding": null, "delimiter": null, "compression": null, "checksum": null, "file_path": "C:\\Users\\<USER>\\Desktop\\AIMetaHarvest\\app\\uploads\\20250607_185804_farmer_advisor_dataset.csv", "upload_date": "2025-06-07T17:58:04.436000", "last_modified": "2025-06-07T18:02:49.408000"}, "data_quality_metrics": {"completeness_score": null, "consistency_score": null, "accuracy_score": null, "validity_score": null, "uniqueness_score": null, "missing_values_count": null, "duplicate_records_count": null}, "statistical_summary": null, "data_lineage": {"source_system": null, "collection_method": null, "processing_history": null, "transformation_applied": null, "validation_rules": null}}