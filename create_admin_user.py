#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the default admin user for AIMetaHarvest.
This script will create the admin user with credentials: admin / admin123
"""

import sys
import os
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def create_admin_user():
    """Create the default admin user"""
    try:
        # Import required modules
        import mongoengine
        from app.models.user import User
        from app.config import Config
        
        print("🔧 Creating Admin User for AIMetaHarvest")
        print("=" * 50)
        
        # Connect to MongoDB
        print("📡 Connecting to MongoDB...")
        config = Config()
        mongodb_uri = config.MONGODB_SETTINGS['host']
        
        try:
            mongoengine.connect(host=mongodb_uri, serverSelectionTimeoutMS=5000)
            print(f"✅ Connected to MongoDB: {mongodb_uri}")
        except Exception as e:
            print(f"❌ Failed to connect to MongoDB: {e}")
            print("💡 Make sure MongoDB is running:")
            print("   Windows: net start MongoDB")
            print("   macOS: brew services start mongodb/brew/mongodb-community")
            print("   Linux: sudo systemctl start mongod")
            return False
        
        # Check if admin user already exists
        print("\n🔍 Checking for existing admin user...")
        existing_admin = User.find_by_username('admin')
        
        if existing_admin:
            print("ℹ️  Admin user already exists!")
            print(f"   Username: admin")
            print(f"   Email: {existing_admin.email}")
            print(f"   Is Admin: {existing_admin.is_admin}")
            print(f"   Created: {existing_admin.created_at}")
            
            # Ask if user wants to reset password
            response = input("\n🔄 Do you want to reset the admin password to 'admin123'? (y/N): ")
            if response.lower() in ['y', 'yes']:
                existing_admin.password = 'admin123'
                existing_admin.save()
                print("✅ Admin password reset to 'admin123'")
            else:
                print("ℹ️  Admin user unchanged")
            
            return True
        
        # Create new admin user
        print("👤 Creating new admin user...")
        admin_user = User.create(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_admin=True
        )
        
        print("✅ Admin user created successfully!")
        print(f"   Username: admin")
        print(f"   Password: admin123")
        print(f"   Email: {admin_user.email}")
        print(f"   User ID: {admin_user.id}")
        print(f"   Created: {admin_user.created_at}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you're running this from the project root directory")
        print("💡 Make sure the virtual environment is activated")
        return False
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        return False

def verify_admin_login():
    """Verify that the admin user can log in"""
    try:
        from app.models.user import User
        
        print("\n🔐 Verifying admin login...")
        admin_user = User.find_by_username('admin')
        
        if not admin_user:
            print("❌ Admin user not found")
            return False
        
        # Test password verification
        if admin_user.verify_password('admin123'):
            print("✅ Admin login verification successful")
            print("🌐 You can now log in at: http://localhost:5001")
            print("   Username: admin")
            print("   Password: admin123")
            return True
        else:
            print("❌ Admin password verification failed")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying admin login: {e}")
        return False

def check_database_indexes():
    """Check and fix database index issues"""
    try:
        from app.models.user import User
        from app.models.dataset import Dataset
        
        print("\n🔧 Checking database indexes...")
        
        # Try to ensure indexes
        try:
            User.ensure_indexes()
            print("✅ User indexes verified")
        except Exception as e:
            print(f"⚠️  User index issue: {e}")
        
        try:
            Dataset.ensure_indexes()
            print("✅ Dataset indexes verified")
        except Exception as e:
            print(f"⚠️  Dataset index issue (this is expected if you have existing data): {e}")
            print("💡 You may need to clear existing datasets to fix unique constraint issues")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking indexes: {e}")
        return False

def main():
    """Main function"""
    print("🚀 AIMetaHarvest Admin User Setup")
    print("=" * 50)
    print(f"Started at: {datetime.now()}")
    print()
    
    # Create admin user
    if not create_admin_user():
        print("\n❌ Failed to create admin user")
        sys.exit(1)
    
    # Verify login
    if not verify_admin_login():
        print("\n❌ Failed to verify admin login")
        sys.exit(1)
    
    # Check database indexes
    check_database_indexes()
    
    print("\n" + "=" * 50)
    print("🎉 Admin user setup completed successfully!")
    print("=" * 50)
    print()
    print("📋 Next steps:")
    print("1. Start the application: python run.py")
    print("2. Open browser to: http://localhost:5001")
    print("3. Login with:")
    print("   Username: admin")
    print("   Password: admin123")
    print()
    print("💡 If you encounter any issues:")
    print("   - Make sure MongoDB is running")
    print("   - Check that the virtual environment is activated")
    print("   - Ensure all dependencies are installed: pip install -r requirements.txt")
    print()

if __name__ == "__main__":
    main()
