"""
Free AI Models Service for Dataset Description Generation
Supports Mistral AI and Groq with robust error handling and text cleaning
"""

import os
import re
import time
import logging
from typing import Optional, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FreeAIService:
    """Service for generating descriptions using free AI models"""
    
    def __init__(self):
        """Initialize the Free AI Service with API clients"""
        self.mistral_client = None
        self.groq_client = None
        self.initialize_clients()
    
    def initialize_clients(self):
        """Initialize API clients for free AI models"""
        # Initialize Mistral AI
        try:
            from mistralai.client import MistralClient
            api_key = os.getenv('MISTRAL_API_KEY')
            if api_key and api_key != 'your_mistral_key_here':
                self.mistral_client = MistralClient(api_key=api_key)
                logger.info("✅ Mistral AI client initialized successfully")
            else:
                logger.warning("⚠️ Mistral API key not found or invalid")
        except ImportError:
            logger.warning("⚠️ Mistral AI package not installed")
        except Exception as e:
            logger.error(f"⚠️ Mistral AI initialization failed: {e}")
        
        # Initialize Groq
        try:
            from groq import Groq
            api_key = os.getenv('GROQ_API_KEY')
            if api_key and api_key != 'your_groq_key_here':
                self.groq_client = Groq(api_key=api_key)
                logger.info("✅ Groq client initialized successfully")
            else:
                logger.warning("⚠️ Groq API key not found or invalid")
        except ImportError:
            logger.warning("⚠️ Groq package not installed")
        except Exception as e:
            logger.error(f"⚠️ Groq initialization failed: {e}")
    
    def clean_generated_text(self, text: str) -> str:
        """
        Clean generated text by removing unnecessary characters and formatting
        
        Args:
            text: Raw generated text
            
        Returns:
            Cleaned text without special characters
        """
        if not text or not isinstance(text, str):
            return ""
        
        # Remove markdown formatting
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)  # Bold
        text = re.sub(r'\*([^*]+)\*', r'\1', text)      # Italic
        text = re.sub(r'`([^`]+)`', r'\1', text)        # Code
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[#@$%^&*+=\[\]{}|\\<>~`]', '', text)
        
        # Clean up multiple spaces and newlines
        text = re.sub(r'\n+', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        
        # Remove leading/trailing whitespace
        text = text.strip()
        
        # Ensure proper sentence structure
        if text and not text.endswith('.'):
            text += '.'
        
        return text
    
    def generate_description_mistral(self, dataset_info: Dict[str, Any]) -> Optional[str]:
        """
        Generate description using Mistral AI
        
        Args:
            dataset_info: Dictionary containing dataset information
            
        Returns:
            Generated description or None if failed
        """
        if not self.mistral_client:
            return None
        
        try:
            # Prepare prompt for Mistral
            prompt = self._create_description_prompt(dataset_info)
            
            from mistralai.models.chat_completion import ChatMessage
            
            messages = [
                ChatMessage(
                    role="system",
                    content="You are a data science expert. Generate clear, professional dataset descriptions without markdown formatting or special characters. Focus on content, structure, and potential use cases."
                ),
                ChatMessage(
                    role="user",
                    content=prompt
                )
            ]
            
            # Generate response
            response = self.mistral_client.chat(
                model="mistral-small",
                messages=messages,
                max_tokens=800,
                temperature=0.3
            )
            
            if response and response.choices:
                description = response.choices[0].message.content
                cleaned_description = self.clean_generated_text(description)
                logger.info(f"✅ Mistral description generated: {len(cleaned_description)} chars")
                return cleaned_description
            
        except Exception as e:
            logger.error(f"⚠️ Mistral description generation failed: {e}")
        
        return None
    
    def generate_description_groq(self, dataset_info: Dict[str, Any]) -> Optional[str]:
        """
        Generate description using Groq
        
        Args:
            dataset_info: Dictionary containing dataset information
            
        Returns:
            Generated description or None if failed
        """
        if not self.groq_client:
            return None
        
        try:
            # Prepare prompt for Groq
            prompt = self._create_description_prompt(dataset_info)
            
            # Generate response
            response = self.groq_client.chat.completions.create(
                model="llama3-8b-8192",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a data science expert. Generate clear, professional dataset descriptions without markdown formatting or special characters. Focus on content, structure, and potential use cases."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=800,
                temperature=0.3
            )
            
            if response and response.choices:
                description = response.choices[0].message.content
                cleaned_description = self.clean_generated_text(description)
                logger.info(f"✅ Groq description generated: {len(cleaned_description)} chars")
                return cleaned_description
            
        except Exception as e:
            logger.error(f"⚠️ Groq description generation failed: {e}")
        
        return None
    
    def _create_description_prompt(self, dataset_info: Dict[str, Any]) -> str:
        """
        Create a comprehensive prompt for AI description generation
        
        Args:
            dataset_info: Dictionary containing dataset information
            
        Returns:
            Formatted prompt string
        """
        title = dataset_info.get('title', 'Dataset')
        record_count = dataset_info.get('record_count', 0)
        field_count = len(dataset_info.get('field_names', []))
        field_names = dataset_info.get('field_names', [])
        data_types = dataset_info.get('data_types', {})
        category = dataset_info.get('category', 'General')
        
        prompt = f"""Generate a comprehensive description for this dataset:

Title: {title}
Category: {category}
Records: {record_count:,}
Fields: {field_count}

Field Information:
"""
        
        # Add field details
        if field_names:
            for field in field_names[:10]:  # Limit to first 10 fields
                field_type = data_types.get(field, 'unknown')
                prompt += f"- {field} ({field_type})\n"
        
        prompt += f"""
Please provide a detailed description that includes:
1. What this dataset contains and represents
2. The structure and organization of the data
3. Potential research applications and use cases
4. Key insights about the data characteristics
5. Suitable analysis methods or techniques

Write in clear, professional language without markdown formatting or special characters. Make it informative and suitable for academic or research purposes."""
        
        return prompt
    
    def generate_enhanced_description(self, dataset_info: Dict[str, Any]) -> Optional[str]:
        """
        Generate enhanced description using available free AI models
        
        Args:
            dataset_info: Dictionary containing dataset information
            
        Returns:
            Generated description or None if all models fail
        """
        # Try Mistral first (usually higher quality)
        description = self.generate_description_mistral(dataset_info)
        if description and len(description) > 100:
            return description
        
        # Fallback to Groq (faster)
        description = self.generate_description_groq(dataset_info)
        if description and len(description) > 100:
            return description
        
        logger.warning("⚠️ All free AI models failed or returned short descriptions")
        return None
    
    def is_available(self) -> bool:
        """Check if any free AI models are available"""
        return self.mistral_client is not None or self.groq_client is not None


# Global instance
free_ai_service = FreeAIService()
