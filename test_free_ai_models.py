#!/usr/bin/env python3
"""
Test script for Free AI Models (Mistral, Groq) and FLAN-T5 Base
Tests the enhanced description generation capabilities
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_free_ai_models():
    """Test Free AI models for description generation"""
    print("🧪 Testing Free AI Models for Description Generation")
    print("=" * 60)
    
    # Test dataset info
    test_dataset_info = {
        'title': 'Customer Sales Data',
        'record_count': 15000,
        'field_names': ['customer_id', 'product_name', 'sales_amount', 'purchase_date', 'region'],
        'data_types': {'customer_id': 'int64', 'product_name': 'object', 'sales_amount': 'float64', 'purchase_date': 'datetime64', 'region': 'object'},
        'category': 'Business',
        'keywords': ['sales', 'customer', 'revenue', 'business', 'analytics']
    }
    
    # Test Free AI Service
    try:
        from app.services.free_ai_service import free_ai_service
        print("✅ Free AI Service imported successfully")
        
        # Test if clients are initialized
        if free_ai_service.mistral_client:
            print("✅ Mistral AI client initialized")
        else:
            print("⚠️ Mistral AI client not initialized (check API key)")
            
        if free_ai_service.groq_client:
            print("✅ Groq client initialized")
        else:
            print("⚠️ Groq client not initialized (check API key)")
        
        # Test description generation
        if free_ai_service.is_available():
            print("\n🔄 Testing Free AI description generation...")
            description = free_ai_service.generate_enhanced_description(test_dataset_info)
            
            if description and len(description) > 100:
                print(f"✅ Free AI description generated successfully!")
                print(f"📝 Description length: {len(description)} characters")
                print(f"📄 Preview: {description[:200]}...")
                return True
            else:
                print(f"⚠️ Free AI returned short description: {description}")
        else:
            print("⚠️ No Free AI models available")
            
    except ImportError as e:
        print(f"❌ Failed to import Free AI Service: {e}")
    except Exception as e:
        print(f"❌ Free AI test failed: {e}")
    
    return False

def test_flan_t5_model():
    """Test FLAN-T5 Base model"""
    print("\n🧪 Testing FLAN-T5 Base Model")
    print("=" * 40)
    
    try:
        from app.services.nlp_service import nlp_service
        print("✅ NLP Service imported successfully")
        
        # Check if FLAN-T5 is available
        if nlp_service.t5_model and nlp_service.t5_tokenizer:
            print("✅ FLAN-T5 Base model loaded successfully")
            
            # Test dataset info
            test_dataset_info = {
                'title': 'Weather Monitoring Dataset',
                'record_count': 8500,
                'field_names': ['timestamp', 'temperature', 'humidity', 'pressure', 'location'],
                'data_types': {'timestamp': 'datetime64', 'temperature': 'float64', 'humidity': 'float64', 'pressure': 'float64', 'location': 'object'},
                'category': 'Environmental',
                'keywords': ['weather', 'climate', 'monitoring', 'environmental', 'sensors']
            }
            
            print("\n🔄 Testing FLAN-T5 description generation...")
            description = nlp_service._generate_description_t5(test_dataset_info)
            
            if description and len(description) > 50:
                print(f"✅ FLAN-T5 description generated successfully!")
                print(f"📝 Description length: {len(description)} characters")
                print(f"📄 Preview: {description[:200]}...")
                return True
            else:
                print(f"⚠️ FLAN-T5 returned short description: {description}")
        else:
            print("⚠️ FLAN-T5 Base model not available")
            
    except ImportError as e:
        print(f"❌ Failed to import NLP Service: {e}")
    except Exception as e:
        print(f"❌ FLAN-T5 test failed: {e}")
    
    return False

def test_enhanced_description_pipeline():
    """Test the complete enhanced description pipeline"""
    print("\n🧪 Testing Complete Enhanced Description Pipeline")
    print("=" * 50)
    
    try:
        from app.services.nlp_service import nlp_service
        
        # Test dataset info
        test_dataset_info = {
            'title': 'E-commerce Product Reviews',
            'record_count': 25000,
            'field_names': ['review_id', 'product_id', 'rating', 'review_text', 'reviewer_name', 'review_date'],
            'data_types': {'review_id': 'int64', 'product_id': 'object', 'rating': 'int64', 'review_text': 'object', 'reviewer_name': 'object', 'review_date': 'datetime64'},
            'category': 'E-commerce',
            'keywords': ['reviews', 'products', 'ratings', 'customers', 'feedback', 'sentiment']
        }
        
        print("🔄 Testing complete enhanced description generation...")
        description = nlp_service.generate_enhanced_description(test_dataset_info)
        
        if description and len(description) > 100:
            print(f"✅ Enhanced description generated successfully!")
            print(f"📝 Description length: {len(description)} characters")
            print(f"📄 Full description:")
            print("-" * 60)
            print(description)
            print("-" * 60)
            
            # Check for special characters
            special_chars = ['#', '*', '`', '@', '$', '%', '^', '&', '+', '=', '[', ']', '{', '}', '|', '\\', '<', '>', '~']
            found_special = [char for char in special_chars if char in description]
            
            if found_special:
                print(f"⚠️ Found special characters that should be cleaned: {found_special}")
            else:
                print("✅ Description is clean (no unwanted special characters)")
            
            return True
        else:
            print(f"⚠️ Enhanced description generation failed or returned short text: {description}")
            
    except Exception as e:
        print(f"❌ Enhanced description pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
    
    return False

def check_api_keys():
    """Check if API keys are configured"""
    print("\n🔑 Checking API Key Configuration")
    print("=" * 40)
    
    api_keys = {
        'MISTRAL_API_KEY': os.getenv('MISTRAL_API_KEY'),
        'GROQ_API_KEY': os.getenv('GROQ_API_KEY')
    }
    
    configured_keys = 0
    for key_name, key_value in api_keys.items():
        if key_value and key_value != f'your_{key_name.lower()}_here':
            print(f"✅ {key_name}: Configured")
            configured_keys += 1
        else:
            print(f"⚠️ {key_name}: Not configured")
    
    if configured_keys > 0:
        print(f"\n✅ {configured_keys}/2 API keys configured")
        return True
    else:
        print("\n⚠️ No API keys configured. Free AI models will not work.")
        print("Please add your API keys to the .env file:")
        print("MISTRAL_API_KEY=your_mistral_key_here")
        print("GROQ_API_KEY=your_groq_key_here")
        return False

def main():
    """Main test function"""
    print("🚀 Free AI Models and FLAN-T5 Base Test Suite")
    print("=" * 60)
    
    # Check API keys
    api_keys_ok = check_api_keys()
    
    # Test Free AI models
    free_ai_ok = test_free_ai_models()
    
    # Test FLAN-T5 model
    flan_t5_ok = test_flan_t5_model()
    
    # Test complete pipeline
    pipeline_ok = test_enhanced_description_pipeline()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    print(f"API Keys Configuration: {'✅ PASS' if api_keys_ok else '⚠️ PARTIAL'}")
    print(f"Free AI Models: {'✅ PASS' if free_ai_ok else '❌ FAIL'}")
    print(f"FLAN-T5 Base Model: {'✅ PASS' if flan_t5_ok else '❌ FAIL'}")
    print(f"Enhanced Pipeline: {'✅ PASS' if pipeline_ok else '❌ FAIL'}")
    
    if pipeline_ok:
        print("\n🎉 All tests passed! Your enhanced description generation is working!")
        print("\n📋 What's working:")
        if free_ai_ok:
            print("• Free AI models (Mistral/Groq) for high-quality descriptions")
        if flan_t5_ok:
            print("• FLAN-T5 Base model for offline description generation")
        print("• Text cleaning to remove special characters")
        print("• Fallback system ensures descriptions are always generated")
    else:
        print("\n⚠️ Some tests failed. Check the error messages above.")
        if not api_keys_ok:
            print("• Configure API keys for Free AI models")
        if not flan_t5_ok:
            print("• FLAN-T5 Base model may need to be downloaded")

if __name__ == "__main__":
    main()
