#!/usr/bin/env python3
"""
Comprehensive test script for FLAN-T5 Base and Free AI integration
Tests offline functionality, model availability, and complete app workflow
"""

import os
import sys
import tempfile
import json
import pandas as pd
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_flan_t5_offline():
    """Test FLAN-T5 Base model offline functionality"""
    print("🔌 Testing FLAN-T5 Base Offline Functionality")
    print("=" * 50)
    
    try:
        # Temporarily set offline mode
        os.environ["HF_HUB_OFFLINE"] = "1"
        os.environ["TRANSFORMERS_OFFLINE"] = "1"
        
        from transformers import T5Tokenizer, T5ForConditionalGeneration
        import torch
        
        print("📥 Loading FLAN-T5 Base in offline mode...")
        
        # Try to load model offline
        tokenizer = T5Tokenizer.from_pretrained(
            "google/flan-t5-base",
            local_files_only=True,
            legacy=False
        )
        model = T5ForConditionalGeneration.from_pretrained(
            "google/flan-t5-base",
            local_files_only=True
        )
        
        print("✅ FLAN-T5 Base loaded successfully in offline mode")
        
        # Test description generation
        test_prompts = [
            "summarize: Customer purchase dataset with demographics and transaction history for business analytics",
            "describe: Scientific research data containing experimental measurements and statistical observations",
            "explain: Financial records dataset with account information and transaction details for audit analysis"
        ]
        
        results = []
        for i, prompt in enumerate(test_prompts, 1):
            print(f"🔄 Testing offline generation {i}/3...")
            
            inputs = tokenizer.encode(prompt, return_tensors="pt", max_length=512, truncation=True)
            
            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    max_length=150,
                    min_length=30,
                    length_penalty=1.5,
                    num_beams=4,
                    early_stopping=True,
                    do_sample=False,
                    repetition_penalty=1.2
                )
            
            result = tokenizer.decode(outputs[0], skip_special_tokens=True)
            results.append(result)
            print(f"✅ Generated {len(result)} characters")
            print(f"   Preview: {result[:80]}...")
        
        # Verify quality
        avg_length = sum(len(r) for r in results) / len(results)
        print(f"\n📊 Offline Generation Statistics:")
        print(f"   Average length: {avg_length:.1f} characters")
        print(f"   All generations successful: {len(results) == 3}")
        
        return True, results
        
    except Exception as e:
        print(f"❌ Offline test failed: {e}")
        return False, []
    finally:
        # Restore online mode
        if "HF_HUB_OFFLINE" in os.environ:
            del os.environ["HF_HUB_OFFLINE"]
        if "TRANSFORMERS_OFFLINE" in os.environ:
            del os.environ["TRANSFORMERS_OFFLINE"]

def test_free_ai_models():
    """Test Free AI models integration"""
    print("\n🤖 Testing Free AI Models Integration")
    print("=" * 50)
    
    try:
        from app.services.free_ai_service import free_ai_service
        print("✅ Free AI Service imported successfully")
        
        # Check API keys
        mistral_configured = bool(os.getenv('MISTRAL_API_KEY') and 
                                os.getenv('MISTRAL_API_KEY') != 'your_mistral_key_here')
        groq_configured = bool(os.getenv('GROQ_API_KEY') and 
                             os.getenv('GROQ_API_KEY') != 'your_groq_key_here')
        
        print(f"🔑 Mistral API Key: {'✅ Configured' if mistral_configured else '⚠️ Not configured'}")
        print(f"🔑 Groq API Key: {'✅ Configured' if groq_configured else '⚠️ Not configured'}")
        
        if not (mistral_configured or groq_configured):
            print("⚠️ No Free AI models configured - skipping API tests")
            return False, "No API keys configured"
        
        # Test description generation
        test_dataset = {
            'title': 'E-commerce Customer Behavior Dataset',
            'record_count': 25000,
            'field_names': ['customer_id', 'product_id', 'purchase_amount', 'timestamp', 'category', 'rating'],
            'data_types': {'customer_id': 'int64', 'product_id': 'object', 'purchase_amount': 'float64', 'timestamp': 'datetime64', 'category': 'object', 'rating': 'int64'},
            'category': 'E-commerce',
            'keywords': ['customer', 'behavior', 'purchase', 'analytics', 'e-commerce']
        }
        
        print("🔄 Testing Free AI description generation...")
        description = free_ai_service.generate_enhanced_description(test_dataset)
        
        if description and len(description) > 100:
            print(f"✅ Free AI description generated: {len(description)} characters")
            
            # Check for special characters
            special_chars = ['#', '*', '`', '@', '$', '%', '^', '&']
            found_special = [char for char in special_chars if char in description]
            
            if found_special:
                print(f"⚠️ Found special characters: {found_special}")
            else:
                print("✅ Description is clean (no special characters)")
            
            print(f"📄 Preview: {description[:150]}...")
            return True, description
        else:
            print(f"⚠️ Free AI returned short or empty description: {description}")
            return False, description
            
    except Exception as e:
        print(f"❌ Free AI test failed: {e}")
        return False, str(e)

def test_enhanced_nlp_service():
    """Test enhanced NLP service with fallback system"""
    print("\n🧠 Testing Enhanced NLP Service")
    print("=" * 50)
    
    try:
        from app.services.nlp_service import nlp_service
        print("✅ NLP Service imported successfully")
        
        # Check model availability
        if nlp_service.t5_model and nlp_service.t5_tokenizer:
            model_name = getattr(nlp_service.t5_model.config, '_name_or_path', 'unknown')
            print(f"✅ T5 Model loaded: {model_name}")
            
            if 'flan-t5-base' in model_name.lower():
                print("🎉 FLAN-T5 Base is active!")
            elif 't5-small' in model_name.lower():
                print("⚠️ T5-small is active (fallback)")
        else:
            print("❌ No T5 model available")
        
        # Test description generation with fallback system
        test_datasets = [
            {
                'title': 'Healthcare Patient Records',
                'record_count': 12000,
                'field_names': ['patient_id', 'age', 'diagnosis', 'treatment', 'outcome'],
                'category': 'Healthcare',
                'keywords': ['healthcare', 'patient', 'medical', 'treatment']
            },
            {
                'title': 'Financial Market Data',
                'record_count': 50000,
                'field_names': ['symbol', 'price', 'volume', 'timestamp', 'exchange'],
                'category': 'Finance',
                'keywords': ['finance', 'market', 'trading', 'stocks']
            }
        ]
        
        results = []
        for i, dataset in enumerate(test_datasets, 1):
            print(f"\n🔄 Testing dataset {i}: {dataset['title']}")
            description = nlp_service.generate_enhanced_description(dataset)
            
            if description and len(description) > 100:
                print(f"✅ Description generated: {len(description)} characters")
                results.append(description)
                
                # Quality checks
                title_mentioned = dataset['title'].lower() in description.lower()
                category_mentioned = dataset['category'].lower() in description.lower()
                
                print(f"📊 Quality: Title mentioned: {title_mentioned}, Category mentioned: {category_mentioned}")
                print(f"📄 Preview: {description[:100]}...")
            else:
                print(f"❌ Description generation failed: {description}")
        
        success_rate = len(results) / len(test_datasets)
        print(f"\n📊 Success Rate: {success_rate:.1%} ({len(results)}/{len(test_datasets)})")
        
        return success_rate >= 0.5, results
        
    except Exception as e:
        print(f"❌ Enhanced NLP service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False, []

def test_complete_workflow():
    """Test complete dataset processing workflow"""
    print("\n🔬 Testing Complete Dataset Processing Workflow")
    print("=" * 50)
    
    try:
        # Create test dataset
        print("📊 Creating test dataset...")
        test_data = {
            'product_id': [f'PROD_{i:04d}' for i in range(1, 501)],
            'product_name': [f'Product {i}' for i in range(1, 501)],
            'category': ['Electronics', 'Clothing', 'Books', 'Home', 'Sports'] * 100,
            'price': [10.0 + (i * 0.5) for i in range(500)],
            'rating': [1 + (i % 5) for i in range(500)],
            'reviews_count': [10 + (i * 2) for i in range(500)]
        }
        
        df = pd.DataFrame(test_data)
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            test_file_path = f.name
        
        print(f"✅ Test dataset created: {len(df)} records, {len(df.columns)} columns")
        
        # Test processing service
        from app.services.processing_service import processing_service
        from app.services.dataset_service import get_dataset_service
        print("✅ Processing service imported")

        # Create mock dataset
        class MockDataset:
            def __init__(self):
                self.title = "Product Catalog Analysis Dataset"
                self.description = "Comprehensive product information for e-commerce analysis"
                self.category = "E-commerce"
                self.file_path = test_file_path
                self.format = "CSV"
                self.size = os.path.getsize(test_file_path)

        mock_dataset = MockDataset()

        # Get dataset service
        dataset_service = get_dataset_service("uploads")

        # Process dataset file
        print("🔄 Processing dataset...")
        result = processing_service._process_dataset_file(mock_dataset, dataset_service)
        
        if result:
            print("✅ Dataset processing completed")
            
            # Check results
            checks = {
                'description': 'description' in result and len(result.get('description', '')) > 100,
                'keywords': 'keywords' in result and len(result.get('keywords', [])) > 0,
                'field_analysis': 'field_analysis' in result and len(result.get('field_analysis', {})) > 0,
                'quality_score': 'quality_score' in result
            }
            
            print("📊 Processing Results:")
            for check, passed in checks.items():
                print(f"   {check}: {'✅ PASS' if passed else '❌ FAIL'}")
            
            if checks['description']:
                description = result['description']
                print(f"\n📝 Generated Description ({len(description)} chars):")
                print(f"   {description[:200]}...")
                
                # Check for special characters
                special_chars = ['#', '*', '`', '@', '$', '%', '^', '&']
                found_special = [char for char in special_chars if char in description]
                
                if found_special:
                    print(f"⚠️ Found special characters: {found_special}")
                else:
                    print("✅ Description is clean")
            
            success = sum(checks.values()) >= 3
            return success, result
        else:
            print("❌ Dataset processing failed")
            return False, None
            
    except Exception as e:
        print(f"❌ Complete workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None
    finally:
        # Cleanup
        if 'test_file_path' in locals():
            Path(test_file_path).unlink(missing_ok=True)

def main():
    """Run comprehensive functionality tests"""
    print("🚀 Comprehensive FLAN-T5 Base & Free AI Functionality Test")
    print("=" * 70)
    
    # Test results
    results = {}
    
    # Test 1: FLAN-T5 Offline
    offline_ok, offline_results = test_flan_t5_offline()
    results['offline'] = offline_ok
    
    # Test 2: Free AI Models
    free_ai_ok, free_ai_result = test_free_ai_models()
    results['free_ai'] = free_ai_ok
    
    # Test 3: Enhanced NLP Service
    nlp_ok, nlp_results = test_enhanced_nlp_service()
    results['nlp'] = nlp_ok
    
    # Test 4: Complete Workflow
    workflow_ok, workflow_result = test_complete_workflow()
    results['workflow'] = workflow_ok
    
    # Final Summary
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 70)
    
    print(f"🔌 FLAN-T5 Offline: {'✅ PASS' if results['offline'] else '❌ FAIL'}")
    print(f"🤖 Free AI Models: {'✅ PASS' if results['free_ai'] else '⚠️ PARTIAL'}")
    print(f"🧠 Enhanced NLP: {'✅ PASS' if results['nlp'] else '❌ FAIL'}")
    print(f"🔬 Complete Workflow: {'✅ PASS' if results['workflow'] else '❌ FAIL'}")
    
    # Overall assessment
    critical_tests = [results['offline'], results['nlp'], results['workflow']]
    passed_critical = sum(critical_tests)
    
    print(f"\n📈 Overall Status: {passed_critical}/3 critical tests passed")
    
    if passed_critical >= 2:
        print("\n🎉 SYSTEM IS FUNCTIONAL!")
        print("✅ Your enhanced description generation system is working!")
        
        if results['offline']:
            print("• FLAN-T5 Base model works offline")
        if results['free_ai']:
            print("• Free AI models are available")
        if results['nlp']:
            print("• Enhanced NLP service is operational")
        if results['workflow']:
            print("• Complete dataset processing works")
            
        print("\n🚀 Ready for production use!")
        
    else:
        print("\n⚠️ SYSTEM NEEDS ATTENTION")
        print("Some critical components are not working properly.")
        
        if not results['offline']:
            print("❌ FLAN-T5 Base offline functionality failed")
            print("   - Check if model is properly downloaded")
            print("   - Verify transformers library installation")
        
        if not results['nlp']:
            print("❌ Enhanced NLP service failed")
            print("   - Check NLP service configuration")
            print("   - Verify model initialization")
        
        if not results['workflow']:
            print("❌ Complete workflow failed")
            print("   - Check processing service")
            print("   - Verify database connectivity")
    
    print("\n" + "=" * 70)
    return all(critical_tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
